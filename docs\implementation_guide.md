# 🚀 新网站爬虫开发完整指南

## 📅 时间规划

### 第1天：网站分析
- [ ] 使用 `tools/website_analyzer.py` 分析目标网站
- [ ] 运行 `tools/anti_crawler_detector.py` 检测反爬机制
- [ ] 分析API接口和数据结构
- [ ] 制定爬取策略

### 第2-3天：开发数据提取逻辑
- [ ] 创建数据模型和字段映射
- [ ] 实现JSON和HTML数据提取
- [ ] 开发数据清洗和验证逻辑
- [ ] 测试数据提取准确性

### 第4-6天：处理反爬策略
- [ ] 实现基础反检测措施
- [ ] 处理特定的反爬机制
- [ ] 集成代理和频率控制
- [ ] 测试绕过效果

### 第7天：集成和测试
- [ ] 集成到MediaCrawler框架
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 文档编写

## 🛠️ 详细实施步骤

### 步骤1：网站分析 (第1天)

#### 1.1 运行网站分析器
```bash
cd tools
python website_analyzer.py
```

修改 `website_analyzer.py` 中的目标URL：
```python
# 在文件末尾修改
if __name__ == "__main__":
    analyzer = WebsiteAnalyzer("https://your-target-site.com")
    
    target_urls = [
        "https://your-target-site.com/",
        "https://your-target-site.com/search?q=test",
        "https://your-target-site.com/category/tech"
    ]
    
    report = analyzer.generate_analysis_report(target_urls)
```

#### 1.2 运行反爬虫检测
```bash
python anti_crawler_detector.py
```

#### 1.3 手动分析补充
1. **打开浏览器开发者工具**
   - 访问目标网站
   - 打开Network面板
   - 执行搜索或浏览操作
   - 记录所有API请求

2. **分析关键API**
   - 搜索API：`/api/search?q=keyword`
   - 详情API：`/api/detail?id=123`
   - 列表API：`/api/list?page=1`

3. **分析数据结构**
   ```json
   {
     "code": 200,
     "data": {
       "items": [
         {
           "id": "123",
           "title": "标题",
           "content": "内容",
           "author": "作者",
           "created_at": "2024-01-01"
         }
       ],
       "total": 100,
       "page": 1
     }
   }
   ```

### 步骤2：创建爬虫模块 (第2-3天)

#### 2.1 创建目录结构
```bash
mkdir -p media_platform/your_site
cd media_platform/your_site
touch __init__.py core.py client.py login.py field.py data_extractor.py exception.py
```

#### 2.2 定义数据字段 (field.py)
```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class YourSitePost:
    post_id: str
    title: str
    content: str
    author: str
    publish_time: str
    url: str
    view_count: int = 0
    like_count: int = 0
    comment_count: int = 0
    
@dataclass
class YourSiteComment:
    comment_id: str
    post_id: str
    content: str
    author: str
    publish_time: str
    like_count: int = 0
```

#### 2.3 实现HTTP客户端 (client.py)
```python
import httpx
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin

class YourSiteClient:
    def __init__(self, proxy: Optional[str] = None, headers: Dict[str, str] = None):
        self.base_url = "https://your-target-site.com"
        self.session = httpx.AsyncClient(
            proxies=proxy,
            headers=headers or {},
            timeout=30
        )
    
    async def search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """搜索接口"""
        url = urljoin(self.base_url, "/api/search")
        response = await self.session.get(url, params=params)
        return response.json()
    
    async def get_post_detail(self, post_id: str) -> Dict[str, Any]:
        """获取帖子详情"""
        url = urljoin(self.base_url, f"/api/post/{post_id}")
        response = await self.session.get(url)
        return response.json()
```

#### 2.4 实现数据提取器 (data_extractor.py)
基于 `tools/data_extractor_template.py` 进行定制：

```python
from tools.data_extractor_template import DataExtractor
from .field import YourSitePost, YourSiteComment

class YourSiteDataExtractor(DataExtractor):
    def __init__(self, base_url: str):
        super().__init__(base_url)
        
        # 设置字段映射
        self.set_field_mapping({
            'title': ['title', 'name', 'subject'],
            'content': ['content', 'body', 'description'],
            'author': ['author', 'username', 'creator'],
            # ... 其他字段映射
        })
    
    def extract_search_results(self, data: Dict[str, Any]) -> List[YourSitePost]:
        """提取搜索结果"""
        items = data.get('data', {}).get('items', [])
        results = []
        
        for item in items:
            extracted = self.extract_from_json(item, [
                'post_id', 'title', 'content', 'author', 'publish_time', 'url'
            ])
            cleaned = self.clean_and_validate_data(extracted)
            
            post = YourSitePost(**cleaned)
            results.append(post)
        
        return results
```

### 步骤3：处理反爬策略 (第4-6天)

#### 3.1 分析检测报告
根据 `anti_crawler_detector.py` 的输出，针对性处理：

```python
# 如果检测到User-Agent敏感
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}

# 如果检测到Referer检查
headers["Referer"] = "https://your-target-site.com"

# 如果检测到频率限制
import asyncio
await asyncio.sleep(random.uniform(1, 3))
```

#### 3.2 实现登录处理 (login.py)
```python
from playwright.async_api import BrowserContext, Page

class YourSiteLogin:
    def __init__(self, login_type: str, browser_context: BrowserContext, 
                 context_page: Page, cookie_str: str = ""):
        self.login_type = login_type
        self.browser_context = browser_context
        self.context_page = context_page
        self.cookie_str = cookie_str
    
    async def begin(self):
        """开始登录流程"""
        if self.login_type == "qrcode":
            await self.login_by_qrcode()
        elif self.login_type == "cookie":
            await self.login_by_cookies()
    
    async def login_by_qrcode(self):
        """二维码登录"""
        # 实现二维码登录逻辑
        pass
```

#### 3.3 集成反爬虫绕过
```python
from tools.anti_crawler_bypass import AntiCrawlerBypass

class YourSiteCrawler(AbstractCrawler):
    def __init__(self):
        super().__init__()
        self.bypass = AntiCrawlerBypass()
    
    async def start(self):
        # 设置隐身浏览器
        await self.bypass.setup_stealth_browser(self.context_page)
        
        # 模拟人类行为
        await self.bypass.simulate_human_behavior(self.context_page)
```

### 步骤4：集成到框架 (第7天)

#### 4.1 注册爬虫
在 `main.py` 中添加：
```python
from media_platform.your_site import YourSiteCrawler

class CrawlerFactory:
    CRAWLERS = {
        "xhs": XiaoHongShuCrawler,
        "dy": DouYinCrawler,
        "ks": KuaiShouCrawler,
        "your_site": YourSiteCrawler,  # 添加新爬虫
    }
```

#### 4.2 添加配置
在 `config/base_config.py` 中添加：
```python
# 您的网站特定配置
YOUR_SITE_POST_IDS = []  # 指定帖子ID列表
YOUR_SITE_CREATOR_IDS = []  # 指定创作者ID列表
```

#### 4.3 测试运行
```bash
# 修改配置
PLATFORM = "your_site"
KEYWORDS = "测试关键词"
CRAWLER_TYPE = "search"

# 运行爬虫
python main.py
```

## 🔧 调试和优化

### 常见问题解决

1. **请求被拒绝 (403/429)**
   - 检查User-Agent和Referer
   - 增加请求间隔
   - 使用代理IP

2. **数据提取失败**
   - 检查API响应格式变化
   - 更新字段映射规则
   - 验证选择器准确性

3. **登录失效**
   - 检查Cookie有效期
   - 实现自动重新登录
   - 使用持久化会话

### 性能优化

1. **并发控制**
   ```python
   semaphore = asyncio.Semaphore(5)  # 限制并发数
   
   async def limited_request():
       async with semaphore:
           return await make_request()
   ```

2. **缓存机制**
   ```python
   import aioredis
   
   redis = aioredis.from_url("redis://localhost")
   await redis.set(f"cache:{key}", json.dumps(data), ex=3600)
   ```

3. **错误重试**
   ```python
   from tenacity import retry, stop_after_attempt, wait_exponential
   
   @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
   async def robust_request():
       return await make_request()
   ```

## 📊 监控和维护

### 日志监控
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
```

### 数据质量检查
```python
def validate_data_quality(data_list: List[Dict]) -> Dict[str, Any]:
    """检查数据质量"""
    total = len(data_list)
    valid = sum(1 for item in data_list if item.get('title') and item.get('content'))
    
    return {
        'total_count': total,
        'valid_count': valid,
        'quality_rate': valid / total if total > 0 else 0
    }
```

## 🎯 成功标准

- [ ] 能够成功访问目标网站
- [ ] 能够绕过主要的反爬机制
- [ ] 数据提取准确率 > 95%
- [ ] 爬取速度满足需求
- [ ] 程序稳定运行 > 24小时
- [ ] 错误处理完善
- [ ] 日志记录详细

完成以上步骤后，您就拥有了一个完整的、针对特定网站的爬虫系统！
