#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个视频爬取工具

专门用于爬取指定的抖音视频信息
"""

import asyncio
import json
import os
import sys
import re
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def extract_video_info_from_share_text(share_text):
    """从分享文本中提取视频信息"""
    # 查找各种可能的链接格式
    patterns = [
        r'https://v\.douyin\.com/([a-zA-Z0-9\-]+)',  # v.douyin.com短链接
        r'https://www\.douyin\.com/video/(\d+)',     # 标准链接
        r'(\d{19})',  # 直接的视频ID（19位数字）
    ]

    for pattern in patterns:
        match = re.search(pattern, share_text)
        if match:
            return match.group(0) if pattern.startswith('http') else match.group(1)

    return None


def setup_single_video_config(video_identifier):
    """设置单个视频爬取配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.CRAWLER_TYPE = "detail"  # 详情模式
    config.CRAWLER_MAX_NOTES_COUNT = 1  # 只爬取一个视频
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = True  # 获取评论信息
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口

    # 根据不同的输入格式设置DY_SPECIFIED_ID_LIST
    if video_identifier.startswith('http'):
        # 完整的URL
        config.DY_SPECIFIED_ID_LIST = [video_identifier]
    elif len(video_identifier) == 19 and video_identifier.isdigit():
        # 19位数字ID
        config.DY_SPECIFIED_ID_LIST = [video_identifier]
    else:
        # 短链接标识符，需要构造完整URL
        config.DY_SPECIFIED_ID_LIST = [f"https://v.douyin.com/{video_identifier}"]


def load_video_data():
    """加载爬取的视频数据"""
    data_dir = "data/douyin"
    results = []
    
    if not os.path.exists(data_dir):
        return results
    
    # 查找最新的JSON文件
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json') and ('detail' in file or 'contents' in file):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")
    
    return results


def display_video_info(video_data):
    """显示视频详细信息"""
    if not video_data:
        print("❌ 没有获取到视频数据")
        return
    
    video = video_data[0] if isinstance(video_data, list) else video_data
    
    print("\n🎬 视频信息:")
    print("=" * 60)
    
    # 基本信息
    title = video.get('title', '无标题')
    author = video.get('nickname', '未知作者')
    aweme_id = video.get('aweme_id', '')
    
    print(f"📝 标题: {title}")
    print(f"👤 作者: {author}")
    print(f"🆔 视频ID: {aweme_id}")
    
    # 统计数据
    likes = video.get('liked_count', 0)
    comments = video.get('comment_count', 0)
    shares = video.get('share_count', 0)
    collects = video.get('collected_count', 0)
    
    print(f"👍 点赞: {likes}")
    print(f"💬 评论: {comments}")
    print(f"🔄 分享: {shares}")
    print(f"⭐ 收藏: {collects}")
    
    # 链接信息
    aweme_url = video.get('aweme_url', '')
    if aweme_url:
        print(f"🔗 分享链接: {aweme_url}")
    
    # 发布时间
    create_time = video.get('create_time', 0)
    if create_time:
        try:
            from datetime import datetime
            dt = datetime.fromtimestamp(create_time)
            print(f"📅 发布时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
        except:
            print(f"📅 发布时间: {create_time}")
    
    # IP位置
    ip_location = video.get('ip_location', '')
    if ip_location:
        print(f"📍 IP位置: {ip_location}")
    
    print("=" * 60)


def save_video_info(video_data):
    """保存视频信息到文件"""
    if not video_data:
        return
    
    video = video_data[0] if isinstance(video_data, list) else video_data
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细信息
    filename = f"video_info_{timestamp}.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频详细信息\n")
        f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"标题: {video.get('title', '无标题')}\n")
        f.write(f"作者: {video.get('nickname', '未知作者')}\n")
        f.write(f"视频ID: {video.get('aweme_id', '')}\n")
        f.write(f"分享链接: {video.get('aweme_url', '')}\n")
        f.write(f"点赞数: {video.get('liked_count', 0)}\n")
        f.write(f"评论数: {video.get('comment_count', 0)}\n")
        f.write(f"分享数: {video.get('share_count', 0)}\n")
        f.write(f"收藏数: {video.get('collected_count', 0)}\n")
        
        # 发布时间
        create_time = video.get('create_time', 0)
        if create_time:
            try:
                dt = datetime.fromtimestamp(create_time)
                f.write(f"发布时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}\n")
            except:
                f.write(f"发布时间: {create_time}\n")
        
        # IP位置
        ip_location = video.get('ip_location', '')
        if ip_location:
            f.write(f"IP位置: {ip_location}\n")
    
    print(f"💾 详细信息已保存到: {filename}")
    
    # 只保存链接
    link_filename = f"video_link_{timestamp}.txt"
    with open(link_filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频链接\n")
        f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"{video.get('aweme_url', '')}\n")
    
    print(f"🔗 链接文件已保存到: {link_filename}")


async def main():
    """主函数"""
    print("🎬 单个视频爬取工具")
    print("=" * 50)
    
    # 获取用户输入
    share_text = input("请粘贴抖音分享文本或链接: ").strip()
    if not share_text:
        print("❌ 必须输入分享文本或链接")
        return
    
    # 提取视频信息
    video_identifier = extract_video_info_from_share_text(share_text)
    if not video_identifier:
        print("❌ 无法从输入中提取有效的视频ID或链接")
        print("💡 请确保包含 v.douyin.com 链接或标准抖音链接")
        return

    print(f"✅ 提取到视频标识: {video_identifier}")
    print()

    input("按回车键开始爬取...")

    # 设置配置
    setup_single_video_config(video_identifier)
    
    print("\n🚀 开始爬取视频信息...")
    print("请准备手机扫码登录抖音!")
    print("-" * 50)
    
    try:
        # 运行爬虫
        await crawler_main()
        
        print("\n🎉 爬取完成!")
        
        # 加载并显示视频数据
        video_data = load_video_data()
        
        if video_data:
            display_video_info(video_data)
            save_video_info(video_data)
            
            print("\n✅ 视频信息获取完成!")
            print("📁 详细信息和链接已保存到文件")
        else:
            print("❌ 没有获取到视频数据")
            print("💡 可能的原因:")
            print("   - 视频不存在或已被删除")
            print("   - 视频设置了隐私权限")
            print("   - 网络连接问题")
        
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("💡 请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
