__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


from cv2.gapi import core as core
from cv2.gapi import ie as ie
from cv2.gapi import imgproc as imgproc
from cv2.gapi import oak as oak
from cv2.gapi import onnx as onnx
from cv2.gapi import ot as ot
from cv2.gapi import ov as ov
from cv2.gapi import own as own
from cv2.gapi import render as render
from cv2.gapi import streaming as streaming
from cv2.gapi import video as video
from cv2.gapi import wip as wip


# Enumerations
StereoOutputFormat_DEPTH_FLOAT16: int
STEREO_OUTPUT_FORMAT_DEPTH_FLOAT16: int
StereoOutputFormat_DEPTH_FLOAT32: int
STEREO_OUTPUT_FORMAT_DEPTH_FLOAT32: int
StereoOutputFormat_DISPARITY_FIXED16_11_5: int
STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_11_5: int
StereoOutputFormat_DISPARITY_FIXED16_12_4: int
STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_12_4: int
StereoOutputFormat_DEPTH_16F: int
STEREO_OUTPUT_FORMAT_DEPTH_16F: int
StereoOutputFormat_DEPTH_32F: int
STEREO_OUTPUT_FORMAT_DEPTH_32F: int
StereoOutputFormat_DISPARITY_16Q_10_5: int
STEREO_OUTPUT_FORMAT_DISPARITY_16Q_10_5: int
StereoOutputFormat_DISPARITY_16Q_11_4: int
STEREO_OUTPUT_FORMAT_DISPARITY_16Q_11_4: int
StereoOutputFormat = int
"""One of [StereoOutputFormat_DEPTH_FLOAT16, STEREO_OUTPUT_FORMAT_DEPTH_FLOAT16, StereoOutputFormat_DEPTH_FLOAT32, STEREO_OUTPUT_FORMAT_DEPTH_FLOAT32, StereoOutputFormat_DISPARITY_FIXED16_11_5, STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_11_5, StereoOutputFormat_DISPARITY_FIXED16_12_4, STEREO_OUTPUT_FORMAT_DISPARITY_FIXED16_12_4, StereoOutputFormat_DEPTH_16F, STEREO_OUTPUT_FORMAT_DEPTH_16F, StereoOutputFormat_DEPTH_32F, STEREO_OUTPUT_FORMAT_DEPTH_32F, StereoOutputFormat_DISPARITY_16Q_10_5, STEREO_OUTPUT_FORMAT_DISPARITY_16Q_10_5, StereoOutputFormat_DISPARITY_16Q_11_4, STEREO_OUTPUT_FORMAT_DISPARITY_16Q_11_4]"""

CV_BOOL: int
CV_INT: int
CV_INT64: int
CV_UINT64: int
CV_DOUBLE: int
CV_FLOAT: int
CV_STRING: int
CV_POINT: int
CV_POINT2F: int
CV_POINT3F: int
CV_SIZE: int
CV_RECT: int
CV_SCALAR: int
CV_MAT: int
CV_GMAT: int
CV_DRAW_PRIM: int
CV_ANY: int
ArgType = int
"""One of [CV_BOOL, CV_INT, CV_INT64, CV_UINT64, CV_DOUBLE, CV_FLOAT, CV_STRING, CV_POINT, CV_POINT2F, CV_POINT3F, CV_SIZE, CV_RECT, CV_SCALAR, CV_MAT, CV_GMAT, CV_DRAW_PRIM, CV_ANY]"""



# Classes
class GNetParam:
    ...

class GNetPackage:
    # Functions
    @_typing.overload
    def __init__(self) -> None: ...
    @_typing.overload
    def __init__(self, nets: _typing.Sequence[GNetParam]) -> None: ...



# Functions
def BGR2Gray(src: cv2.GMat) -> cv2.GMat: ...

def BGR2I420(src: cv2.GMat) -> cv2.GMat: ...

def BGR2LUV(src: cv2.GMat) -> cv2.GMat: ...

def BGR2RGB(src: cv2.GMat) -> cv2.GMat: ...

def BGR2YUV(src: cv2.GMat) -> cv2.GMat: ...

def BayerGR2RGB(src_gr: cv2.GMat) -> cv2.GMat: ...

def Canny(image: cv2.GMat, threshold1: float, threshold2: float, apertureSize: int = ..., L2gradient: bool = ...) -> cv2.GMat: ...

def I4202BGR(src: cv2.GMat) -> cv2.GMat: ...

def I4202RGB(src: cv2.GMat) -> cv2.GMat: ...

def LUT(src: cv2.GMat, lut: cv2.typing.MatLike) -> cv2.GMat: ...

def LUV2BGR(src: cv2.GMat) -> cv2.GMat: ...

def Laplacian(src: cv2.GMat, ddepth: int, ksize: int = ..., scale: float = ..., delta: float = ..., borderType: int = ...) -> cv2.GMat: ...

def NV12toBGR(src_y: cv2.GMat, src_uv: cv2.GMat) -> cv2.GMat: ...

def NV12toGray(src_y: cv2.GMat, src_uv: cv2.GMat) -> cv2.GMat: ...

def NV12toRGB(src_y: cv2.GMat, src_uv: cv2.GMat) -> cv2.GMat: ...

@_typing.overload
def RGB2Gray(src: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def RGB2Gray(src: cv2.GMat, rY: float, gY: float, bY: float) -> cv2.GMat: ...

def RGB2HSV(src: cv2.GMat) -> cv2.GMat: ...

def RGB2I420(src: cv2.GMat) -> cv2.GMat: ...

def RGB2Lab(src: cv2.GMat) -> cv2.GMat: ...

def RGB2YUV(src: cv2.GMat) -> cv2.GMat: ...

def RGB2YUV422(src: cv2.GMat) -> cv2.GMat: ...

def Sobel(src: cv2.GMat, ddepth: int, dx: int, dy: int, ksize: int = ..., scale: float = ..., delta: float = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def SobelXY(src: cv2.GMat, ddepth: int, order: int, ksize: int = ..., scale: float = ..., delta: float = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> tuple[cv2.GMat, cv2.GMat]: ...

def YUV2BGR(src: cv2.GMat) -> cv2.GMat: ...

def YUV2RGB(src: cv2.GMat) -> cv2.GMat: ...

def absDiff(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...

def absDiffC(src: cv2.GMat, c: cv2.GScalar) -> cv2.GMat: ...

def add(src1: cv2.GMat, src2: cv2.GMat, ddepth: int = ...) -> cv2.GMat: ...

@_typing.overload
def addC(src1: cv2.GMat, c: cv2.GScalar, ddepth: int = ...) -> cv2.GMat: ...
@_typing.overload
def addC(c: cv2.GScalar, src1: cv2.GMat, ddepth: int = ...) -> cv2.GMat: ...

def addWeighted(src1: cv2.GMat, alpha: float, src2: cv2.GMat, beta: float, gamma: float, ddepth: int = ...) -> cv2.GMat: ...

def bilateralFilter(src: cv2.GMat, d: int, sigmaColor: float, sigmaSpace: float, borderType: int = ...) -> cv2.GMat: ...

@_typing.overload
def bitwise_and(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def bitwise_and(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

def bitwise_not(src: cv2.GMat) -> cv2.GMat: ...

@_typing.overload
def bitwise_or(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def bitwise_or(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def bitwise_xor(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def bitwise_xor(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

def blur(src: cv2.GMat, ksize: cv2.typing.Size, anchor: cv2.typing.Point = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

@_typing.overload
def boundingRect(src: cv2.GMat) -> cv2.GOpaqueT: ...
@_typing.overload
def boundingRect(src: cv2.GArrayT) -> cv2.GOpaqueT: ...
@_typing.overload
def boundingRect(src: cv2.GArrayT) -> cv2.GOpaqueT: ...

def boxFilter(src: cv2.GMat, dtype: int, ksize: cv2.typing.Size, anchor: cv2.typing.Point = ..., normalize: bool = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def cartToPolar(x: cv2.GMat, y: cv2.GMat, angleInDegrees: bool = ...) -> tuple[cv2.GMat, cv2.GMat]: ...

@_typing.overload
def cmpEQ(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpEQ(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def cmpGE(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpGE(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def cmpGT(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpGT(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def cmpLE(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpLE(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def cmpLT(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpLT(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def cmpNE(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def cmpNE(src1: cv2.GMat, src2: cv2.GScalar) -> cv2.GMat: ...

def combine(lhs: cv2.GKernelPackage, rhs: cv2.GKernelPackage) -> cv2.GKernelPackage: ...

@_typing.overload
def concatHor(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def concatHor(v: _typing.Sequence[cv2.GMat]) -> cv2.GMat: ...

@_typing.overload
def concatVert(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...
@_typing.overload
def concatVert(v: _typing.Sequence[cv2.GMat]) -> cv2.GMat: ...

def convertTo(src: cv2.GMat, rdepth: int, alpha: float = ..., beta: float = ...) -> cv2.GMat: ...

def copy(in_: cv2.GMat) -> cv2.GMat: ...

def countNonZero(src: cv2.GMat) -> cv2.GOpaqueT: ...

def crop(src: cv2.GMat, rect: cv2.typing.Rect) -> cv2.GMat: ...

def dilate(src: cv2.GMat, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., iterations: int = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def dilate3x3(src: cv2.GMat, iterations: int = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def div(src1: cv2.GMat, src2: cv2.GMat, scale: float, ddepth: int = ...) -> cv2.GMat: ...

def divC(src: cv2.GMat, divisor: cv2.GScalar, scale: float, ddepth: int = ...) -> cv2.GMat: ...

def divRC(divident: cv2.GScalar, src: cv2.GMat, scale: float, ddepth: int = ...) -> cv2.GMat: ...

def equalizeHist(src: cv2.GMat) -> cv2.GMat: ...

def erode(src: cv2.GMat, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., iterations: int = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def erode3x3(src: cv2.GMat, iterations: int = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def filter2D(src: cv2.GMat, ddepth: int, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., delta: cv2.typing.Scalar = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def flip(src: cv2.GMat, flipCode: int) -> cv2.GMat: ...

def gaussianBlur(src: cv2.GMat, ksize: cv2.typing.Size, sigmaX: float, sigmaY: float = ..., borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def goodFeaturesToTrack(image: cv2.GMat, maxCorners: int, qualityLevel: float, minDistance: float, mask: cv2.typing.MatLike | None = ..., blockSize: int = ..., useHarrisDetector: bool = ..., k: float = ...) -> cv2.GArrayT: ...

def inRange(src: cv2.GMat, threshLow: cv2.GScalar, threshUp: cv2.GScalar) -> cv2.GMat: ...

@_typing.overload
def infer(name: str, inputs: cv2.GInferInputs) -> cv2.GInferOutputs: ...
@_typing.overload
def infer(name: str, roi: cv2.GOpaqueT, inputs: cv2.GInferInputs) -> cv2.GInferOutputs: ...
@_typing.overload
def infer(name: str, rois: cv2.GArrayT, inputs: cv2.GInferInputs) -> cv2.GInferListOutputs: ...

def infer2(name: str, in_: cv2.GMat, inputs: cv2.GInferListInputs) -> cv2.GInferListOutputs: ...

def integral(src: cv2.GMat, sdepth: int = ..., sqdepth: int = ...) -> tuple[cv2.GMat, cv2.GMat]: ...

@_typing.overload
def kmeans(data: cv2.GMat, K: int, bestLabels: cv2.GMat, criteria: cv2.typing.TermCriteria, attempts: int, flags: cv2.KmeansFlags) -> tuple[cv2.GOpaqueT, cv2.GMat, cv2.GMat]: ...
@_typing.overload
def kmeans(data: cv2.GMat, K: int, criteria: cv2.typing.TermCriteria, attempts: int, flags: cv2.KmeansFlags) -> tuple[cv2.GOpaqueT, cv2.GMat, cv2.GMat]: ...
@_typing.overload
def kmeans(data: cv2.GArrayT, K: int, bestLabels: cv2.GArrayT, criteria: cv2.typing.TermCriteria, attempts: int, flags: cv2.KmeansFlags) -> tuple[cv2.GOpaqueT, cv2.GArrayT, cv2.GArrayT]: ...
@_typing.overload
def kmeans(data: cv2.GArrayT, K: int, bestLabels: cv2.GArrayT, criteria: cv2.typing.TermCriteria, attempts: int, flags: cv2.KmeansFlags) -> tuple[cv2.GOpaqueT, cv2.GArrayT, cv2.GArrayT]: ...

def mask(src: cv2.GMat, mask: cv2.GMat) -> cv2.GMat: ...

def max(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...

def mean(src: cv2.GMat) -> cv2.GScalar: ...

def medianBlur(src: cv2.GMat, ksize: int) -> cv2.GMat: ...

def merge3(src1: cv2.GMat, src2: cv2.GMat, src3: cv2.GMat) -> cv2.GMat: ...

def merge4(src1: cv2.GMat, src2: cv2.GMat, src3: cv2.GMat, src4: cv2.GMat) -> cv2.GMat: ...

def min(src1: cv2.GMat, src2: cv2.GMat) -> cv2.GMat: ...

def morphologyEx(src: cv2.GMat, op: cv2.MorphTypes, kernel: cv2.typing.MatLike, anchor: cv2.typing.Point = ..., iterations: int = ..., borderType: cv2.BorderTypes = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def mul(src1: cv2.GMat, src2: cv2.GMat, scale: float = ..., ddepth: int = ...) -> cv2.GMat: ...

@_typing.overload
def mulC(src: cv2.GMat, multiplier: float, ddepth: int = ...) -> cv2.GMat: ...
@_typing.overload
def mulC(src: cv2.GMat, multiplier: cv2.GScalar, ddepth: int = ...) -> cv2.GMat: ...
@_typing.overload
def mulC(multiplier: cv2.GScalar, src: cv2.GMat, ddepth: int = ...) -> cv2.GMat: ...

def normInf(src: cv2.GMat) -> cv2.GScalar: ...

def normL1(src: cv2.GMat) -> cv2.GScalar: ...

def normL2(src: cv2.GMat) -> cv2.GScalar: ...

def normalize(src: cv2.GMat, alpha: float, beta: float, norm_type: int, ddepth: int = ...) -> cv2.GMat: ...

@_typing.overload
def parseSSD(in_: cv2.GMat, inSz: cv2.GOpaqueT, confidenceThreshold: float = ..., filterLabel: int = ...) -> tuple[cv2.GArrayT, cv2.GArrayT]: ...
@_typing.overload
def parseSSD(in_: cv2.GMat, inSz: cv2.GOpaqueT, confidenceThreshold: float, alignmentToSquare: bool, filterOutOfBounds: bool) -> cv2.GArrayT: ...

def parseYolo(in_: cv2.GMat, inSz: cv2.GOpaqueT, confidenceThreshold: float = ..., nmsThreshold: float = ..., anchors: _typing.Sequence[float] = ...) -> tuple[cv2.GArrayT, cv2.GArrayT]: ...

def phase(x: cv2.GMat, y: cv2.GMat, angleInDegrees: bool = ...) -> cv2.GMat: ...

def polarToCart(magnitude: cv2.GMat, angle: cv2.GMat, angleInDegrees: bool = ...) -> tuple[cv2.GMat, cv2.GMat]: ...

def remap(src: cv2.GMat, map1: cv2.typing.MatLike, map2: cv2.typing.MatLike, interpolation: int, borderMode: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def resize(src: cv2.GMat, dsize: cv2.typing.Size, fx: float = ..., fy: float = ..., interpolation: int = ...) -> cv2.GMat: ...

def select(src1: cv2.GMat, src2: cv2.GMat, mask: cv2.GMat) -> cv2.GMat: ...

def sepFilter(src: cv2.GMat, ddepth: int, kernelX: cv2.typing.MatLike, kernelY: cv2.typing.MatLike, anchor: cv2.typing.Point, delta: cv2.typing.Scalar, borderType: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def split3(src: cv2.GMat) -> tuple[cv2.GMat, cv2.GMat, cv2.GMat]: ...

def split4(src: cv2.GMat) -> tuple[cv2.GMat, cv2.GMat, cv2.GMat, cv2.GMat]: ...

def sqrt(src: cv2.GMat) -> cv2.GMat: ...

def sub(src1: cv2.GMat, src2: cv2.GMat, ddepth: int = ...) -> cv2.GMat: ...

def subC(src: cv2.GMat, c: cv2.GScalar, ddepth: int = ...) -> cv2.GMat: ...

def subRC(c: cv2.GScalar, src: cv2.GMat, ddepth: int = ...) -> cv2.GMat: ...

def sum(src: cv2.GMat) -> cv2.GScalar: ...

@_typing.overload
def threshold(src: cv2.GMat, thresh: cv2.GScalar, maxval: cv2.GScalar, type: int) -> cv2.GMat: ...
@_typing.overload
def threshold(src: cv2.GMat, maxval: cv2.GScalar, type: int) -> tuple[cv2.GMat, cv2.GScalar]: ...

def transpose(src: cv2.GMat) -> cv2.GMat: ...

def warpAffine(src: cv2.GMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...

def warpPerspective(src: cv2.GMat, M: cv2.typing.MatLike, dsize: cv2.typing.Size, flags: int = ..., borderMode: int = ..., borderValue: cv2.typing.Scalar = ...) -> cv2.GMat: ...


