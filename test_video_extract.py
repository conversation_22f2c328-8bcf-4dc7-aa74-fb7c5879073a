#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频链接提取
"""

import re


def extract_video_info_from_share_text(share_text):
    """从分享文本中提取视频信息"""
    print(f"输入文本: {share_text}")
    
    # 查找各种可能的链接格式
    patterns = [
        (r'https://v\.douyin\.com/([a-zA-Z0-9\-]+)', "短链接"),
        (r'https://www\.douyin\.com/video/(\d+)', "标准链接"),
        (r'(\d{19})', "视频ID"),
    ]
    
    for pattern, desc in patterns:
        matches = re.findall(pattern, share_text)
        if matches:
            print(f"找到{desc}: {matches}")
            return matches[0] if len(matches[0]) > 10 else f"https://v.douyin.com/{matches[0]}"
    
    return None


def main():
    print("🧪 测试视频链接提取")
    print("=" * 40)
    
    # 测试你的分享文本
    share_text = "6.66 复制打开抖音，看看【程序员王老师的作品】今天心情堵得慌 我知道AI编程已经到来，自己要接受... https://v.douyin.com/feDuNe0NS-0/ Zzt:/ 11/19 <EMAIL>"
    
    result = extract_video_info_from_share_text(share_text)
    
    if result:
        print(f"\n✅ 提取结果: {result}")
        print(f"📱 这个链接可以用来爬取视频信息")
    else:
        print("\n❌ 没有找到有效的视频链接")
    
    print("\n" + "=" * 40)
    print("💡 如果提取成功，你可以使用以下命令爬取:")
    print("   python single_video_crawler.py")
    print("   然后粘贴上面的分享文本")


if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
