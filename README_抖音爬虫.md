# 🎬 抖音视频链接爬虫

## 📖 项目简介

这是一个基于关键词搜索抖音视频并获取视频链接的爬虫工具。已经为你配置好了所有必要的参数，可以直接使用。

## 🚀 快速开始

### 1. 最简单的方式（推荐）

```bash
python quick_start.py
```

按照提示操作：
1. 输入搜索关键词（如：美食,旅游,科技）
2. 设置爬取数量（建议20-50个）
3. 扫码登录抖音
4. 等待爬取完成

### 2. 使用专用爬虫脚本

```bash
python douyin_video_crawler.py
```

这个脚本会提供更详细的输出和结果展示。

### 3. 查看爬取结果

```bash
python view_results.py
```

可以查看数据摘要、导出链接等。

## 📁 文件说明

### 核心脚本
- `quick_start.py` - 快速启动脚本（推荐新手使用）
- `douyin_video_crawler.py` - 专用爬虫脚本（详细输出）
- `view_results.py` - 结果查看器
- `test_config.py` - 配置测试脚本

### 配置文件
- `config/base_config.py` - 主配置文件（已优化）
- `config/dy_config.py` - 抖音专用配置

### 说明文档
- `抖音视频爬虫使用说明.md` - 详细使用说明
- `README_抖音爬虫.md` - 本文件

## ⚙️ 已优化的配置

我已经为你优化了以下配置：

```python
# 搜索关键词（可修改）
KEYWORDS = "美食,旅游,科技"

# 爬取数量
CRAWLER_MAX_NOTES_COUNT = 20

# 不下载视频文件，只获取链接
ENABLE_GET_MEIDAS = False

# 不爬取评论，提高速度
ENABLE_GET_COMMENTS = False

# 保存为JSON格式，方便查看
SAVE_DATA_OPTION = "json"

# 显示浏览器窗口，方便登录
HEADLESS = False

# 增加爬取间隔，避免被限制
CRAWLER_MAX_SLEEP_SEC = 3
```

## 📊 输出结果

爬取完成后，你会得到：

### 1. JSON数据文件
位置：`data/douyin/[时间戳]_search_contents_[日期].json`

每个视频包含：
- 视频标题和作者信息
- 点赞、评论、分享数
- **视频页面链接** (`aweme_url`)
- **视频下载链接** (`video_download_url`)
- 封面图片链接
- 发布时间等

### 2. 格式化的链接文件
使用 `view_results.py` 可以导出易读的文本文件。

## 🎯 使用示例

### 搜索美食相关视频
```bash
python quick_start.py
# 输入关键词：美食,烹饪,菜谱
```

### 搜索科技内容
```bash
python quick_start.py  
# 输入关键词：AI,科技,数码,手机
```

### 搜索旅游视频
```bash
python quick_start.py
# 输入关键词：旅游,风景,旅行,攻略
```

## 🔧 自定义配置

如需修改默认设置，编辑 `config/base_config.py`：

```python
# 修改默认关键词
KEYWORDS = "你的关键词1,你的关键词2"

# 修改爬取数量
CRAWLER_MAX_NOTES_COUNT = 50

# 如果需要下载视频文件
ENABLE_GET_MEIDAS = True
```

## ⚠️ 注意事项

1. **首次使用**需要扫码登录抖音
2. **合理使用**，不要过于频繁爬取
3. **网络稳定**，确保能正常访问抖音
4. **仅供学习**，请遵守平台使用条款

## 🆘 常见问题

### Q: 登录失败怎么办？
A: 确保网络正常，尝试手动在浏览器中登录抖音

### Q: 爬取数量少于预期？
A: 可能是关键词结果有限，尝试更换关键词

### Q: 程序运行缓慢？
A: 这是正常的，为了避免被限制，程序会有间隔时间

### Q: 无法获取下载链接？
A: 部分视频有版权保护，这是正常现象

## 🎉 开始使用

现在你可以直接运行：

```bash
python quick_start.py
```

按照提示操作，几分钟后就能获得抖音视频链接了！

---

**配置完成，祝你使用愉快！** 🚀
