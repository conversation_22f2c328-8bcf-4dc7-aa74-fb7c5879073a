#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试链接提取功能

测试从已有数据中提取不同类型的链接
"""

import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def load_test_data():
    """加载测试数据"""
    data_file = "data/douyin/json/search_contents_2025-09-11.json"
    
    if not os.path.exists(data_file):
        print("❌ 测试数据文件不存在")
        return []
    
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data


def test_link_extraction():
    """测试链接提取功能"""
    print("🧪 测试链接提取功能")
    print("=" * 50)
    
    data = load_test_data()
    if not data:
        return
    
    print(f"📊 加载了 {len(data)} 个视频数据")
    print()
    
    # 显示前3个视频的链接信息
    for i, video in enumerate(data[:3], 1):
        title = video.get('title', '无标题')[:40] + "..."
        author = video.get('nickname', '未知')
        
        print(f"🎬 视频 {i}: {title}")
        print(f"👤 作者: {author}")
        print("🔗 链接类型:")
        
        # 标准链接
        aweme_url = video.get('aweme_url', '')
        if aweme_url:
            print(f"  📱 标准链接: {aweme_url}")
        
        # 下载链接
        download_url = video.get('video_download_url', '')
        if download_url:
            print(f"  🎬 下载链接: {download_url[:60]}...")
        
        # 封面链接
        cover_url = video.get('cover_url', '')
        if cover_url:
            print(f"  🖼️ 封面链接: {cover_url[:60]}...")
        
        # 音乐链接
        music_url = video.get('music_download_url', '')
        if music_url:
            print(f"  🎵 音乐链接: {music_url[:60]}...")
        
        print("-" * 50)
    
    # 统计链接类型
    print("\n📊 链接类型统计:")
    aweme_count = sum(1 for v in data if v.get('aweme_url'))
    download_count = sum(1 for v in data if v.get('video_download_url'))
    cover_count = sum(1 for v in data if v.get('cover_url'))
    music_count = sum(1 for v in data if v.get('music_download_url'))
    
    print(f"📱 标准链接: {aweme_count}/{len(data)} 个")
    print(f"🎬 下载链接: {download_count}/{len(data)} 个")
    print(f"🖼️ 封面链接: {cover_count}/{len(data)} 个")
    print(f"🎵 音乐链接: {music_count}/{len(data)} 个")
    
    return data


def demo_extraction(data, link_type):
    """演示链接提取"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    type_names = {
        1: "标准视频链接",
        2: "视频下载链接", 
        3: "封面图片链接",
        4: "背景音乐链接",
        5: "全部链接类型"
    }
    
    print(f"\n🔗 演示提取: {type_names.get(link_type, '未知类型')}")
    print("-" * 40)
    
    if link_type == 1:  # 标准链接
        filename = f"demo_standard_links_{timestamp}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("📱 抖音标准视频链接 (演示)\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            
            for i, video in enumerate(data[:5], 1):  # 只演示前5个
                title = video.get('title', '无标题')
                author = video.get('nickname', '未知')
                url = video.get('aweme_url', '')
                
                f.write(f"{i}. {title}\n")
                f.write(f"   👤 作者: {author}\n")
                f.write(f"   🔗 链接: {url}\n")
                f.write("-" * 60 + "\n")
        
        print(f"✅ 演示文件已保存: {filename}")
        
        # 显示前2个链接
        for i, video in enumerate(data[:2], 1):
            print(f"{i}. {video.get('title', '无标题')[:30]}...")
            print(f"   🔗 {video.get('aweme_url', '')}")


def main():
    """主函数"""
    print("🧪 链接提取功能测试")
    print("=" * 50)
    
    # 测试数据加载
    data = test_link_extraction()
    if not data:
        return
    
    print("\n🎯 选择要演示的链接类型:")
    print("1. 📱 标准视频链接")
    print("2. 🎬 视频下载链接")
    print("3. 🖼️ 封面图片链接")
    print("4. 🎵 背景音乐链接")
    print("5. 🎯 全部链接类型")
    
    try:
        choice = int(input("\n请选择 (1-5): ").strip())
        if 1 <= choice <= 5:
            demo_extraction(data, choice)
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 请输入数字")
    except KeyboardInterrupt:
        print("\n👋 测试被取消")
    
    print("\n✅ 测试完成!")
    print("💡 现在你可以运行 python quick_start.py 来使用完整功能")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
