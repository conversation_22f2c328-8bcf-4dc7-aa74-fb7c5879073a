# 抖音视频爬虫使用说明

## 📖 简介

这是一个基于关键词搜索抖音视频并获取视频链接的爬虫工具。可以帮助你：

- 🔍 通过关键词搜索抖音视频
- 📱 获取视频页面链接和下载链接
- 📊 获取视频基本信息（标题、作者、点赞数等）
- 💾 支持多种数据保存格式
- 🚀 简单易用的操作界面

## 🛠️ 环境准备

### 1. 安装依赖

确保已安装所有必要的Python包：

```bash
pip install -r requirements.txt
```

### 2. 浏览器准备

程序会自动启动浏览器，建议使用Chrome或Edge浏览器。

## 🚀 快速开始

### 方法一：使用快速启动脚本（推荐新手）

```bash
python quick_start.py
```

1. 运行脚本后，按提示输入搜索关键词
2. 设置爬取数量
3. 程序会自动打开浏览器
4. 使用手机抖音APP扫码登录
5. 等待爬取完成

### 方法二：使用专用爬虫脚本

```bash
python douyin_video_crawler.py
```

这个脚本提供更详细的输出和结果展示。

### 方法三：使用原始main.py

```bash
python main.py --platform dy --type search --keywords "美食,旅游"
```

## 📋 详细使用步骤

### 1. 配置关键词

有三种方式设置搜索关键词：

#### 方式1：运行时输入（推荐）
运行 `quick_start.py` 时会提示输入关键词

#### 方式2：修改配置文件
编辑 `config/base_config.py`：
```python
KEYWORDS = "美食,旅游,科技,音乐"  # 多个关键词用逗号分隔
```

#### 方式3：命令行参数
```bash
python main.py --keywords "美食,旅游,科技"
```

### 2. 设置爬取参数

在 `config/base_config.py` 中可以调整以下参数：

```python
# 爬取数量
CRAWLER_MAX_NOTES_COUNT = 20

# 爬取间隔（秒）
CRAWLER_MAX_SLEEP_SEC = 3

# 是否下载媒体文件（建议设为False，只获取链接）
ENABLE_GET_MEIDAS = False

# 是否爬取评论（建议设为False，只获取视频信息）
ENABLE_GET_COMMENTS = False

# 数据保存格式
SAVE_DATA_OPTION = "json"  # json | csv | db | sqlite
```

### 3. 登录抖音

程序启动后会打开浏览器：

1. 浏览器会自动跳转到抖音登录页面
2. 使用手机抖音APP扫描二维码
3. 完成登录后程序会自动开始爬取

### 4. 查看结果

爬取完成后，数据会保存在 `data/douyin/` 目录下。

#### 使用结果查看器（推荐）

```bash
python view_results.py
```

提供以下功能：
- 📊 显示数据摘要
- 📹 显示视频列表
- 📄 导出链接到文本文件
- 💾 导出数据到JSON文件

#### 直接查看JSON文件

数据文件位置：`data/douyin/[时间戳]_search_contents_[日期].json`

## 📁 输出文件说明

### JSON数据文件

每个视频包含以下信息：

```json
{
  "aweme_id": "视频ID",
  "title": "视频标题",
  "nickname": "作者昵称",
  "liked_count": "点赞数",
  "comment_count": "评论数",
  "share_count": "分享数",
  "aweme_url": "视频页面链接",
  "video_download_url": "视频下载链接",
  "cover_url": "封面图片链接",
  "create_time": "发布时间",
  "ip_location": "IP归属地"
}
```

### 导出的链接文件

包含格式化的视频信息和链接，便于查看和使用。

## ⚙️ 高级配置

### 1. 代理设置

如需使用代理，在 `config/base_config.py` 中设置：

```python
ENABLE_IP_PROXY = True
IP_PROXY_POOL_COUNT = 2
IP_PROXY_PROVIDER_NAME = "kuaidaili"  # 或其他代理商
```

### 2. 浏览器设置

```python
HEADLESS = False  # True为无头模式，False显示浏览器
SAVE_LOGIN_STATE = True  # 保存登录状态，下次无需重新登录
```

### 3. CDP模式（高级用户）

```python
ENABLE_CDP_MODE = True  # 使用现有浏览器实例
CDP_DEBUG_PORT = 9222
```

## 🔧 常见问题

### Q1: 登录失败或被检测

**解决方案：**
- 设置 `HEADLESS = False` 显示浏览器
- 增加 `CRAWLER_MAX_SLEEP_SEC` 爬取间隔
- 尝试使用CDP模式
- 检查网络连接

### Q2: 爬取数量少于预期

**可能原因：**
- 关键词搜索结果有限
- 被反爬虫机制限制
- 网络连接问题

**解决方案：**
- 更换关键词
- 增加爬取间隔时间
- 检查登录状态

### Q3: 无法获取视频下载链接

**说明：**
- 部分视频可能有版权保护
- 某些视频格式特殊
- 这是正常现象，程序会跳过这些视频

### Q4: 程序运行缓慢

**优化建议：**
- 减少爬取数量
- 增加爬取间隔
- 关闭媒体下载功能
- 关闭评论爬取功能

## 📞 技术支持

如遇到问题，请检查：

1. **依赖安装**：确保所有Python包已正确安装
2. **网络连接**：确保能正常访问抖音网站
3. **浏览器版本**：建议使用最新版Chrome或Edge
4. **登录状态**：确保抖音账号登录成功

## ⚠️ 使用须知

1. **合规使用**：请遵守抖音平台的使用条款
2. **频率控制**：不要过于频繁地爬取，避免对平台造成压力
3. **数据用途**：仅用于学习和研究目的
4. **版权尊重**：尊重视频创作者的版权

## 🎯 使用技巧

1. **关键词选择**：选择具体、热门的关键词效果更好
2. **时间安排**：避开高峰时段，减少被限制的可能
3. **数据备份**：定期备份爬取的数据
4. **结果分析**：使用 `view_results.py` 分析数据趋势

---

**祝你使用愉快！** 🎉
