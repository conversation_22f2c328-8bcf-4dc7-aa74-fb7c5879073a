#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频爬虫快速启动脚本

这是一个简化的启动脚本，可以快速配置和运行抖音视频爬虫
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_config(keywords="美食,旅游,科技", max_count=20):
    """设置爬虫配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.KEYWORDS = keywords  # 搜索关键词
    config.CRAWLER_TYPE = "search"  # 搜索模式
    config.CRAWLER_MAX_NOTES_COUNT = max_count  # 最大爬取数量
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 3  # 爬取间隔


def print_banner():
    """打印欢迎信息"""
    print("🎬" + "=" * 60 + "🎬")
    print("           抖音视频链接爬取工具 v1.0")
    print("🎬" + "=" * 60 + "🎬")
    print()
    print("功能说明:")
    print("✅ 通过关键词搜索抖音视频")
    print("✅ 获取视频页面链接和下载链接")
    print("✅ 获取视频基本信息（标题、作者、点赞数等）")
    print("✅ 支持多个关键词同时搜索")
    print("✅ 结果保存为JSON格式，方便查看")
    print()


def choose_link_types():
    """选择要提取的链接类型"""
    print("🔗 选择要提取的链接类型:")
    print("=" * 50)
    print("1. 📱 标准视频链接 (推荐)")
    print("   格式: https://www.douyin.com/video/[ID]")
    print("   用途: 分享给朋友，在浏览器中观看")
    print()
    print("2. 🎬 视频下载链接")
    print("   格式: https://www.douyin.com/aweme/v1/play/...")
    print("   用途: 直接下载视频文件")
    print()
    print("3. 🖼️ 封面图片链接")
    print("   格式: https://p3-pc-sign.douyinpic.com/...")
    print("   用途: 获取视频封面图片")
    print()
    print("4. 🎵 背景音乐链接")
    print("   格式: https://lf26-music-east.douyinstatic.com/...")
    print("   用途: 下载视频背景音乐")
    print()
    print("5. 🎯 全部链接类型")
    print("   包含以上所有类型的链接")
    print()

    while True:
        try:
            choice = input("请选择链接类型 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("❌ 请输入 1-5 之间的数字")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def print_instructions():
    """打印使用说明"""
    print("📋 使用说明:")
    print("1. 程序会自动打开浏览器")
    print("2. 请使用手机抖音APP扫码登录")
    print("3. 登录成功后程序会自动开始爬取")
    print("4. 爬取完成后会根据你的选择提取相应链接")
    print("5. 结果会保存为文本文件，方便查看和使用")
    print()


def load_crawled_data():
    """加载爬取的数据"""
    data_dir = "data/douyin"
    results = []

    if not os.path.exists(data_dir):
        return results

    # 查找最新的JSON文件
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json') and 'contents' in file:
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")

    return results


def extract_links_by_type(data, link_type):
    """根据类型提取链接"""
    if not data:
        print("❌ 没有找到爬取数据")
        return

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if link_type == 1:  # 标准视频链接
        extract_standard_links(data, timestamp)
    elif link_type == 2:  # 视频下载链接
        extract_download_links(data, timestamp)
    elif link_type == 3:  # 封面图片链接
        extract_cover_links(data, timestamp)
    elif link_type == 4:  # 背景音乐链接
        extract_music_links(data, timestamp)
    elif link_type == 5:  # 全部链接
        extract_all_links(data, timestamp)


def extract_standard_links(data, timestamp):
    """提取标准视频链接"""
    filename = f"douyin_standard_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("📱 抖音标准视频链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            url = video.get('aweme_url', '')
            likes = video.get('liked_count', 0)

            f.write(f"{i:2d}. {title}\n")
            f.write(f"    👤 作者: {author}\n")
            f.write(f"    👍 点赞: {likes}\n")
            f.write(f"    🔗 链接: {url}\n")
            f.write("-" * 60 + "\n")

    print(f"✅ 标准链接已保存到: {filename}")


def extract_download_links(data, timestamp):
    """提取视频下载链接"""
    filename = f"douyin_download_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("🎬 抖音视频下载链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("⚠️ 注意: 下载链接包含签名，可能有时效性\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            download_url = video.get('video_download_url', '')

            if download_url:
                f.write(f"{i:2d}. {title}\n")
                f.write(f"    👤 作者: {author}\n")
                f.write(f"    📥 下载: {download_url}\n")
                f.write("-" * 60 + "\n")

    print(f"✅ 下载链接已保存到: {filename}")


def extract_cover_links(data, timestamp):
    """提取封面图片链接"""
    filename = f"douyin_cover_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("🖼️ 抖音视频封面链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            cover_url = video.get('cover_url', '')

            if cover_url:
                f.write(f"{i:2d}. {title}\n")
                f.write(f"    👤 作者: {author}\n")
                f.write(f"    🖼️ 封面: {cover_url}\n")
                f.write("-" * 60 + "\n")

    print(f"✅ 封面链接已保存到: {filename}")


def extract_music_links(data, timestamp):
    """提取背景音乐链接"""
    filename = f"douyin_music_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("🎵 抖音背景音乐链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            music_url = video.get('music_download_url', '')

            if music_url:
                f.write(f"{i:2d}. {title}\n")
                f.write(f"    👤 作者: {author}\n")
                f.write(f"    🎵 音乐: {music_url}\n")
                f.write("-" * 60 + "\n")

    print(f"✅ 音乐链接已保存到: {filename}")


def extract_all_links(data, timestamp):
    """提取所有类型的链接"""
    filename = f"douyin_all_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("🎯 抖音视频全部链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 80 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')

            f.write(f"视频 {i}: {title}\n")
            f.write(f"👤 作者: {author}\n")
            f.write(f"👍 点赞: {video.get('liked_count', 0)} | ")
            f.write(f"💬 评论: {video.get('comment_count', 0)} | ")
            f.write(f"🔄 分享: {video.get('share_count', 0)}\n")
            f.write("\n🔗 链接信息:\n")

            # 标准链接
            if video.get('aweme_url'):
                f.write(f"  📱 标准链接: {video.get('aweme_url')}\n")

            # 下载链接
            if video.get('video_download_url'):
                f.write(f"  🎬 下载链接: {video.get('video_download_url')}\n")

            # 封面链接
            if video.get('cover_url'):
                f.write(f"  🖼️ 封面链接: {video.get('cover_url')}\n")

            # 音乐链接
            if video.get('music_download_url'):
                f.write(f"  🎵 音乐链接: {video.get('music_download_url')}\n")

            f.write("\n" + "=" * 80 + "\n")

    print(f"✅ 全部链接已保存到: {filename}")


async def main():
    """主函数"""
    print_banner()

    # 获取用户输入
    print("🔧 配置参数:")
    keywords = input("请输入搜索关键词（多个用逗号分隔，回车使用默认）: ").strip()
    if not keywords:
        keywords = "美食,旅游,科技"
        print(f"使用默认关键词: {keywords}")

    try:
        max_count = input("请输入最大爬取数量（回车使用默认20）: ").strip()
        max_count = int(max_count) if max_count else 20
    except ValueError:
        max_count = 20
        print(f"使用默认数量: {max_count}")

    print(f"\n✅ 配置完成:")
    print(f"   关键词: {keywords}")
    print(f"   数量: {max_count}")
    print()

    # 选择链接类型
    link_type = choose_link_types()

    print_instructions()

    input("按回车键开始爬取...")

    # 设置配置
    setup_config(keywords, max_count)

    print("\n🚀 开始启动爬虫...")
    print("请准备手机扫码登录抖音!")
    print("-" * 60)

    try:
        # 运行爬虫
        await crawler_main()

        print("\n🎉 爬取完成!")
        print("📁 原始数据保存在 data/douyin/ 目录下")

        # 提取指定类型的链接
        print("\n� 正在提取链接...")
        data = load_crawled_data()
        extract_links_by_type(data, link_type)

        print("\n✅ 链接提取完成!")
        print("💡 提示: 链接文件已保存在当前目录下")

    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("💡 请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
