#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频爬虫快速启动脚本

这是一个简化的启动脚本，可以快速配置和运行抖音视频爬虫
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_search_config(keywords="美食,旅游,科技", max_count=20):
    """设置关键词搜索配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.KEYWORDS = keywords  # 搜索关键词
    config.CRAWLER_TYPE = "search"  # 搜索模式
    config.CRAWLER_MAX_NOTES_COUNT = max_count  # 最大爬取数量
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 3  # 爬取间隔


def setup_creator_config(creator_id, max_count=20):
    """设置抖音号专门爬取配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.CRAWLER_TYPE = "creator"  # 创作者模式
    config.CRAWLER_MAX_NOTES_COUNT = max_count  # 最大爬取数量
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 3  # 爬取间隔

    # 设置要爬取的创作者ID列表
    config.DY_CREATOR_ID_LIST = [creator_id]


def print_banner():
    """打印欢迎信息"""
    print("🎬" + "=" * 60 + "🎬")
    print("           抖音视频链接爬取工具 v2.0")
    print("🎬" + "=" * 60 + "🎬")
    print()
    print("功能说明:")
    print("✅ 通过关键词搜索抖音视频")
    print("✅ 专门爬取指定抖音号的所有视频")
    print("✅ 获取视频页面链接和下载链接")
    print("✅ 获取视频基本信息（标题、作者、点赞数等）")
    print("✅ 支持多种输出格式")
    print("✅ 结果保存为JSON格式，方便查看")
    print()


def choose_crawl_mode():
    """选择爬取模式"""
    print("🎯 选择爬取模式:")
    print("=" * 50)
    print("1. 🔍 关键词搜索模式")
    print("   - 通过关键词搜索相关视频")
    print("   - 可以发现不同作者的视频")
    print("   - 适合寻找特定主题的内容")
    print()
    print("2. 👤 抖音号专门爬取模式")
    print("   - 专门爬取指定抖音号的所有视频")
    print("   - 获取该作者的完整作品列表")
    print("   - 适合关注特定创作者")
    print()

    while True:
        try:
            choice = input("请选择爬取模式 (1-2): ").strip()
            if choice in ['1', '2']:
                return int(choice)
            else:
                print("❌ 请输入 1 或 2")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def get_creator_info():
    """获取抖音号信息"""
    print("\n👤 抖音号专门爬取配置:")
    print("-" * 40)
    print("💡 支持的抖音号格式:")
    print("   1. 抖音号: 如 douyin123456")
    print("   2. 用户主页链接: 如 https://www.douyin.com/user/xxx")
    print("   3. sec_user_id: 如 MS4wLjABAAAA...")
    print()

    creator_input = input("请输入抖音号、主页链接或sec_user_id: ").strip()
    if not creator_input:
        print("❌ 必须输入抖音号信息")
        return None

    # 简单的格式处理
    if creator_input.startswith('https://www.douyin.com/user/'):
        # 从链接中提取sec_user_id
        creator_id = creator_input.split('/user/')[-1].split('?')[0]
    elif creator_input.startswith('MS4wLjABAAAA'):
        # 直接是sec_user_id
        creator_id = creator_input
    else:
        # 假设是抖音号，实际使用时可能需要转换
        creator_id = creator_input
        print("⚠️  注意: 如果是抖音号，可能需要转换为sec_user_id")
        print("💡 建议使用用户主页链接或直接提供sec_user_id")

    return creator_id


def choose_link_types():
    """选择要提取的链接类型"""
    print("🔗 链接输出格式:")
    print("=" * 50)
    print("1. 📱 简洁链接列表 (推荐)")
    print("   - 只输出纯链接，一行一个")
    print("   - 格式最简洁，方便复制使用")
    print("   - 就像你的 douyin_links_only_xxx.txt 文件一样")
    print()
    print("2. 📋 带序号的链接")
    print("   - 每个链接前面有序号")
    print("   - 方便查看和引用")
    print()
    print("3. 📄 详细信息 + 链接")
    print("   - 包含标题、作者等信息")
    print("   - 适合需要了解视频内容的情况")
    print()

    while True:
        try:
            choice = input("请选择输出格式 (1-3，推荐选1): ").strip()
            if choice in ['1', '2', '3']:
                return int(choice)
            elif choice == '':
                return 1  # 默认选择简洁格式
            else:
                print("❌ 请输入 1-3 之间的数字")
        except KeyboardInterrupt:
            raise
        except:
            print("❌ 输入无效，请重新输入")


def print_instructions():
    """打印使用说明"""
    print("📋 使用说明:")
    print("1. 程序会自动打开浏览器")
    print("2. 请使用手机抖音APP扫码登录")
    print("3. 登录成功后程序会自动开始爬取")
    print("4. 爬取完成后会根据你的选择提取相应链接")
    print("5. 结果会保存为文本文件，方便查看和使用")
    print()


def load_crawled_data():
    """加载爬取的数据"""
    data_dir = "data/douyin"
    results = []

    if not os.path.exists(data_dir):
        return results

    # 查找最新的JSON文件
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json') and 'contents' in file:
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")

    return results


def extract_links_by_type(data, link_type):
    """根据类型提取链接"""
    if not data:
        print("❌ 没有找到爬取数据")
        return

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    if link_type == 1:  # 简洁链接列表
        extract_clean_links(data, timestamp)
    elif link_type == 2:  # 带序号的链接
        extract_numbered_links(data, timestamp)
    elif link_type == 3:  # 详细信息 + 链接
        extract_detailed_links(data, timestamp)


def extract_clean_links(data, timestamp):
    """提取简洁链接列表（就像你的douyin_links_only文件一样）"""
    filename = f"douyin_links_only_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频标准链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")

        # 只写链接，一行一个，最简洁的格式
        for video in data:
            url = video.get('aweme_url', '')
            if url:
                f.write(f"{url}\n")

    print(f"✅ 简洁链接已保存到: {filename}")
    print(f"📱 格式和你的 douyin_links_only_xxx.txt 完全一样")


def extract_numbered_links(data, timestamp):
    """提取带序号的链接"""
    filename = f"douyin_numbered_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频链接 (带序号)\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 50 + "\n\n")

        for i, video in enumerate(data, 1):
            url = video.get('aweme_url', '')
            if url:
                f.write(f"{i:2d}. {url}\n")

    print(f"✅ 带序号链接已保存到: {filename}")


def extract_detailed_links(data, timestamp):
    """提取详细信息 + 链接"""
    filename = f"douyin_detailed_links_{timestamp}.txt"

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("抖音视频详细信息\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")

        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            author = video.get('nickname', '未知')
            url = video.get('aweme_url', '')
            likes = video.get('liked_count', 0)

            f.write(f"{i:2d}. {title}\n")
            f.write(f"    👤 作者: {author}\n")
            f.write(f"    👍 点赞: {likes}\n")
            f.write(f"    🔗 链接: {url}\n")
            f.write("-" * 60 + "\n")

    print(f"✅ 详细信息已保存到: {filename}")





async def main():
    """主函数"""
    print_banner()

    # 选择爬取模式
    crawl_mode = choose_crawl_mode()

    if crawl_mode == 1:
        # 关键词搜索模式
        print("\n🔍 关键词搜索模式")
        print("🔧 配置参数:")
        keywords = input("请输入搜索关键词（多个用逗号分隔，回车使用默认）: ").strip()
        if not keywords:
            keywords = "美食,旅游,科技"
            print(f"使用默认关键词: {keywords}")

        try:
            max_count = input("请输入最大爬取数量（回车使用默认20）: ").strip()
            max_count = int(max_count) if max_count else 20
        except ValueError:
            max_count = 20
            print(f"使用默认数量: {max_count}")

        print(f"\n✅ 搜索配置完成:")
        print(f"   模式: 关键词搜索")
        print(f"   关键词: {keywords}")
        print(f"   数量: {max_count}")

        # 选择链接类型
        link_type = choose_link_types()

        print_instructions()
        input("按回车键开始爬取...")

        # 设置搜索配置
        setup_search_config(keywords, max_count)

    elif crawl_mode == 2:
        # 抖音号专门爬取模式
        print("\n👤 抖音号专门爬取模式")
        creator_id = get_creator_info()
        if not creator_id:
            return

        try:
            max_count = input("请输入最大爬取数量（回车使用默认50）: ").strip()
            max_count = int(max_count) if max_count else 50
        except ValueError:
            max_count = 50
            print(f"使用默认数量: {max_count}")

        print(f"\n✅ 抖音号配置完成:")
        print(f"   模式: 抖音号专门爬取")
        print(f"   抖音号: {creator_id}")
        print(f"   数量: {max_count}")

        # 选择链接类型
        link_type = choose_link_types()

        print_instructions()
        input("按回车键开始爬取...")

        # 设置创作者配置
        setup_creator_config(creator_id, max_count)

    print("\n🚀 开始启动爬虫...")
    print("请准备手机扫码登录抖音!")
    print("-" * 60)

    try:
        # 运行爬虫
        await crawler_main()

        print("\n🎉 爬取完成!")
        print("📁 原始数据保存在 data/douyin/ 目录下")

        # 提取指定类型的链接
        print("\n🔗 正在提取链接...")
        data = load_crawled_data()
        extract_links_by_type(data, link_type)

        print("\n✅ 链接提取完成!")
        if crawl_mode == 1:
            print("💡 提示: 已从搜索结果中提取链接")
        else:
            print("💡 提示: 已从指定抖音号中提取链接")
        print("📁 链接文件已保存在当前目录下")

    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("💡 请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
