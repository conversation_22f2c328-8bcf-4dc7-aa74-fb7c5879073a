#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频爬虫快速启动脚本

这是一个简化的启动脚本，可以快速配置和运行抖音视频爬虫
"""

import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_config(keywords="美食,旅游,科技", max_count=20):
    """设置爬虫配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.KEYWORDS = keywords  # 搜索关键词
    config.CRAWLER_TYPE = "search"  # 搜索模式
    config.CRAWLER_MAX_NOTES_COUNT = max_count  # 最大爬取数量
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 3  # 爬取间隔


def print_banner():
    """打印欢迎信息"""
    print("🎬" + "=" * 60 + "🎬")
    print("           抖音视频链接爬取工具 v1.0")
    print("🎬" + "=" * 60 + "🎬")
    print()
    print("功能说明:")
    print("✅ 通过关键词搜索抖音视频")
    print("✅ 获取视频页面链接和下载链接")
    print("✅ 获取视频基本信息（标题、作者、点赞数等）")
    print("✅ 支持多个关键词同时搜索")
    print("✅ 结果保存为JSON格式，方便查看")
    print()


def print_instructions():
    """打印使用说明"""
    print("📋 使用说明:")
    print("1. 程序会自动打开浏览器")
    print("2. 请使用手机抖音APP扫码登录")
    print("3. 登录成功后程序会自动开始爬取")
    print("4. 爬取完成后会在data/douyin目录生成JSON文件")
    print("5. 可以使用douyin_video_crawler.py查看格式化结果")
    print()


async def main():
    """主函数"""
    print_banner()
    
    # 获取用户输入
    print("🔧 配置参数:")
    keywords = input("请输入搜索关键词（多个用逗号分隔，回车使用默认）: ").strip()
    if not keywords:
        keywords = "美食,旅游,科技"
        print(f"使用默认关键词: {keywords}")
    
    try:
        max_count = input("请输入最大爬取数量（回车使用默认20）: ").strip()
        max_count = int(max_count) if max_count else 20
    except ValueError:
        max_count = 20
        print(f"使用默认数量: {max_count}")
    
    print(f"\n✅ 配置完成:")
    print(f"   关键词: {keywords}")
    print(f"   数量: {max_count}")
    print()
    
    print_instructions()
    
    input("按回车键开始爬取...")
    
    # 设置配置
    setup_config(keywords, max_count)
    
    print("\n🚀 开始启动爬虫...")
    print("请准备手机扫码登录抖音!")
    print("-" * 60)
    
    try:
        # 运行爬虫
        await crawler_main()
        
        print("\n🎉 爬取完成!")
        print("📁 结果文件保存在 data/douyin/ 目录下")
        print("💡 提示: 运行 python douyin_video_crawler.py 可以查看格式化的结果")
        
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("💡 请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
