#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取标准抖音链接

从爬取结果中提取标准的抖音视频链接（aweme_url）
"""

import json
import os
from datetime import datetime


def extract_standard_links():
    """提取标准链接"""
    # 读取数据文件
    data_file = "data/douyin/json/search_contents_2025-09-11.json"
    
    if not os.path.exists(data_file):
        print("❌ 数据文件不存在")
        return
    
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("🎬 抖音标准视频链接")
    print("=" * 80)
    print(f"📊 共 {len(data)} 个视频")
    print()
    
    # 提取标准链接
    standard_links = []
    
    for i, video in enumerate(data, 1):
        title = video.get('title', '无标题')[:50] + "..."
        author = video.get('nickname', '未知')
        standard_url = video.get('aweme_url', '')
        likes = video.get('liked_count', 0)
        
        print(f"{i:2d}. {title}")
        print(f"    👤 {author} | 👍 {likes}")
        print(f"    🔗 {standard_url}")
        print()
        
        standard_links.append({
            'index': i,
            'title': video.get('title', ''),
            'author': author,
            'url': standard_url,
            'likes': likes,
            'comments': video.get('comment_count', 0),
            'shares': video.get('share_count', 0)
        })
    
    # 保存到文件
    save_links(standard_links)


def save_links(links):
    """保存链接到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存为纯链接文件
    links_file = f"douyin_links_only_{timestamp}.txt"
    with open(links_file, 'w', encoding='utf-8') as f:
        f.write("抖音视频标准链接\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")
        
        for link in links:
            f.write(f"{link['url']}\n")
    
    print(f"🔗 纯链接文件: {links_file}")
    
    # 保存为详细信息文件
    detail_file = f"douyin_videos_detail_{timestamp}.txt"
    with open(detail_file, 'w', encoding='utf-8') as f:
        f.write("抖音视频详细信息\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")
        
        for link in links:
            f.write(f"视频 {link['index']}: {link['title']}\n")
            f.write(f"作者: {link['author']}\n")
            f.write(f"互动: 👍{link['likes']} 💬{link['comments']} 🔄{link['shares']}\n")
            f.write(f"链接: {link['url']}\n")
            f.write("-" * 80 + "\n")
    
    print(f"📄 详细信息文件: {detail_file}")
    
    # 保存为CSV文件
    csv_file = f"douyin_videos_{timestamp}.csv"
    with open(csv_file, 'w', encoding='utf-8-sig') as f:
        f.write("序号,标题,作者,点赞数,评论数,分享数,视频链接\n")
        for link in links:
            title = link['title'].replace(',', '，').replace('\n', ' ').replace('"', '""')
            f.write(f'{link["index"]},"{title}",{link["author"]},{link["likes"]},{link["comments"]},{link["shares"]},{link["url"]}\n')
    
    print(f"📊 CSV文件: {csv_file}")


if __name__ == "__main__":
    print("🔗 抖音标准链接提取工具")
    print("=" * 50)
    print()
    print("💡 说明: aweme_url 就是标准的抖音视频链接")
    print("💡 这些链接和平常分享时看到的格式完全一样")
    print("💡 可以直接复制粘贴到浏览器或分享给朋友")
    print()
    
    extract_standard_links()
    
    print("\n✅ 提取完成!")
    print("📱 这些就是标准的抖音视频链接，和你平常看到的一模一样！")
