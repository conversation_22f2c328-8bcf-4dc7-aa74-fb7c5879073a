#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音爬取结果查看器

用于查看和导出已爬取的抖音视频数据
"""

import json
import os
from datetime import datetime
from typing import List, Dict


class DouyinResultViewer:
    """抖音结果查看器"""
    
    def __init__(self):
        self.data_dir = "data/douyin"
        self.results = []
    
    def load_latest_results(self) -> bool:
        """加载最新的爬取结果"""
        if not os.path.exists(self.data_dir):
            print("❌ 数据目录不存在，请先运行爬虫")
            return False
        
        # 查找最新的JSON文件
        json_files = [f for f in os.listdir(self.data_dir) 
                     if f.endswith('.json') and 'contents' in f]
        
        if not json_files:
            print("❌ 未找到爬取结果文件")
            return False
        
        # 按修改时间排序，获取最新文件
        latest_file = max(json_files, 
                         key=lambda x: os.path.getmtime(os.path.join(self.data_dir, x)))
        file_path = os.path.join(self.data_dir, latest_file)
        
        print(f"📂 加载文件: {latest_file}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("⚠️  文件为空")
                    return False
                
                # 处理JSONL格式
                lines = content.split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            self.results.append(data)
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ 成功加载 {len(self.results)} 条视频数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return False
    
    def display_summary(self):
        """显示数据摘要"""
        if not self.results:
            return
        
        print("\n📊 数据摘要:")
        print("-" * 50)
        print(f"视频总数: {len(self.results)}")
        
        # 统计作者
        authors = {}
        total_likes = 0
        total_comments = 0
        total_shares = 0
        
        for video in self.results:
            author = video.get('nickname', '未知')
            authors[author] = authors.get(author, 0) + 1
            
            total_likes += int(video.get('liked_count', 0))
            total_comments += int(video.get('comment_count', 0))
            total_shares += int(video.get('share_count', 0))
        
        print(f"作者数量: {len(authors)}")
        print(f"总点赞数: {total_likes:,}")
        print(f"总评论数: {total_comments:,}")
        print(f"总分享数: {total_shares:,}")
        
        # 显示热门作者
        if authors:
            top_authors = sorted(authors.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"\n🔥 热门作者:")
            for author, count in top_authors:
                print(f"   {author}: {count} 个视频")
    
    def display_videos(self, limit: int = 10):
        """显示视频列表"""
        if not self.results:
            return
        
        print(f"\n📹 视频列表 (显示前 {min(limit, len(self.results))} 个):")
        print("=" * 80)
        
        for i, video in enumerate(self.results[:limit], 1):
            title = video.get('title', '无标题')
            if len(title) > 40:
                title = title[:40] + "..."
            
            print(f"\n{i:2d}. {title}")
            print(f"    作者: {video.get('nickname', '未知')}")
            print(f"    互动: 👍{video.get('liked_count', 0)} "
                  f"💬{video.get('comment_count', 0)} "
                  f"🔄{video.get('share_count', 0)}")
            print(f"    链接: {video.get('aweme_url', '无链接')}")
            
            # 显示下载链接（如果有）
            download_url = video.get('video_download_url', '')
            if download_url:
                print(f"    下载: {download_url}")
    
    def export_links(self, filename: str = None):
        """导出视频链接"""
        if not self.results:
            print("❌ 没有数据可导出")
            return
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"douyin_links_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("抖音视频链接导出\n")
                f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"视频数量: {len(self.results)}\n")
                f.write("=" * 60 + "\n\n")
                
                for i, video in enumerate(self.results, 1):
                    f.write(f"{i:3d}. {video.get('title', '无标题')}\n")
                    f.write(f"     作者: {video.get('nickname', '未知')}\n")
                    f.write(f"     页面: {video.get('aweme_url', '无链接')}\n")
                    
                    download_url = video.get('video_download_url', '')
                    if download_url:
                        f.write(f"     下载: {download_url}\n")
                    
                    f.write(f"     点赞: {video.get('liked_count', 0)} | "
                           f"评论: {video.get('comment_count', 0)} | "
                           f"分享: {video.get('share_count', 0)}\n")
                    f.write("-" * 60 + "\n")
            
            print(f"✅ 链接已导出到: {filename}")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")
    
    def export_json(self, filename: str = None):
        """导出为JSON格式"""
        if not self.results:
            print("❌ 没有数据可导出")
            return
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"douyin_data_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 数据已导出到: {filename}")
            
        except Exception as e:
            print(f"❌ 导出失败: {e}")


def main():
    """主函数"""
    print("🔍 抖音爬取结果查看器")
    print("=" * 50)
    
    viewer = DouyinResultViewer()
    
    if not viewer.load_latest_results():
        return
    
    while True:
        print("\n📋 操作菜单:")
        print("1. 显示数据摘要")
        print("2. 显示视频列表")
        print("3. 导出链接到文本文件")
        print("4. 导出数据到JSON文件")
        print("5. 退出")
        
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            viewer.display_summary()
        
        elif choice == '2':
            try:
                limit = int(input("显示多少个视频 (默认10): ").strip() or "10")
            except ValueError:
                limit = 10
            viewer.display_videos(limit)
        
        elif choice == '3':
            filename = input("输入文件名 (回车使用默认): ").strip()
            viewer.export_links(filename if filename else None)
        
        elif choice == '4':
            filename = input("输入文件名 (回车使用默认): ").strip()
            viewer.export_json(filename if filename else None)
        
        elif choice == '5':
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
