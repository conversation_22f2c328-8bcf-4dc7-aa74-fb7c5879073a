# 网站数据结构分析指南

## 🔍 第一阶段：网站基础调研

### 1. 网站架构分析
```bash
# 使用工具分析网站技术栈
whatweb https://target-website.com
wappalyzer https://target-website.com

# 检查robots.txt
curl https://target-website.com/robots.txt

# 检查sitemap
curl https://target-website.com/sitemap.xml
```

### 2. 页面结构分析
- 打开浏览器开发者工具 (F12)
- 分析页面加载过程
- 识别数据来源：
  - [ ] 服务端渲染 (SSR)
  - [ ] 客户端渲染 (CSR/SPA)
  - [ ] 混合渲染

### 3. 数据获取方式识别
```javascript
// 在浏览器控制台执行，检查页面数据
console.log('页面数据源分析：');
console.log('1. HTML中的数据:', document.querySelector('[data-*]'));
console.log('2. JavaScript变量:', window.__INITIAL_STATE__ || window.__DATA__);
console.log('3. localStorage数据:', localStorage);
console.log('4. sessionStorage数据:', sessionStorage);
```

## 🌐 第二阶段：网络请求分析

### 1. 抓包分析工具
- **Chrome DevTools Network面板**
- **Burp Suite** (专业版)
- **OWASP ZAP** (免费)
- **Fiddler** (Windows)
- **Charles Proxy** (跨平台)

### 2. 关键请求识别
```python
# 使用Python脚本自动化分析
import requests
import json
from urllib.parse import urljoin, urlparse

def analyze_api_endpoints(base_url, sample_pages):
    """分析API端点"""
    endpoints = []
    
    for page in sample_pages:
        # 分析页面中的AJAX请求
        # 这里需要结合浏览器抓包结果
        pass
    
    return endpoints

# 示例使用
sample_pages = [
    '/search?q=keyword',
    '/category/tech',
    '/user/profile'
]
```

### 3. 请求参数分析
创建请求分析表格：

| API端点 | 请求方法 | 必需参数 | 可选参数 | 响应格式 | 分页方式 |
|---------|----------|----------|----------|----------|----------|
| /api/search | GET | q, page | limit, sort | JSON | offset/limit |
| /api/detail | GET | id | fields | JSON | 无 |

## 📊 第三阶段：数据结构映射

### 1. 响应数据结构分析
```python
# 创建数据结构分析脚本
import json
import requests
from typing import Dict, Any

def analyze_response_structure(url: str, params: Dict = None) -> Dict[str, Any]:
    """分析API响应结构"""
    response = requests.get(url, params=params)
    data = response.json()
    
    def get_structure(obj, path="root"):
        if isinstance(obj, dict):
            structure = {}
            for key, value in obj.items():
                structure[key] = {
                    'type': type(value).__name__,
                    'path': f"{path}.{key}",
                    'sample': str(value)[:100] if isinstance(value, str) else value
                }
                if isinstance(value, (dict, list)):
                    structure[key]['children'] = get_structure(value, f"{path}.{key}")
            return structure
        elif isinstance(obj, list) and obj:
            return {
                'type': 'list',
                'item_type': type(obj[0]).__name__,
                'length': len(obj),
                'sample_item': get_structure(obj[0], f"{path}[0]") if obj else None
            }
        return {'type': type(obj).__name__, 'value': obj}
    
    return get_structure(data)

# 使用示例
structure = analyze_response_structure('https://api.example.com/search', {'q': 'test'})
print(json.dumps(structure, indent=2, ensure_ascii=False))
```

### 2. 数据字段映射
```python
# 创建字段映射配置
FIELD_MAPPING = {
    'title': ['title', 'name', 'subject', 'headline'],
    'content': ['content', 'body', 'description', 'text'],
    'author': ['author', 'user', 'creator', 'publisher'],
    'publish_time': ['created_at', 'publish_time', 'date', 'timestamp'],
    'url': ['url', 'link', 'href', 'permalink'],
    'id': ['id', 'uuid', 'key', 'identifier']
}

def map_fields(raw_data: Dict, mapping: Dict) -> Dict:
    """根据映射规则提取字段"""
    result = {}
    for target_field, possible_sources in mapping.items():
        for source_field in possible_sources:
            if source_field in raw_data:
                result[target_field] = raw_data[source_field]
                break
    return result
```
