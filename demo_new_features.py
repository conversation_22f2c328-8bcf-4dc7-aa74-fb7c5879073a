#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示新的链接选择功能
"""

import json
import os
from datetime import datetime


def demo_link_selection():
    """演示链接选择功能"""
    print("🎬 抖音爬虫新功能演示")
    print("=" * 50)
    print()
    
    print("🆕 新增功能: 链接类型选择")
    print("现在运行 quick_start.py 时，你可以选择要提取的链接类型:")
    print()
    
    print("🔗 可选择的链接类型:")
    print("1. 📱 标准视频链接 (推荐)")
    print("   - 格式: https://www.douyin.com/video/[ID]")
    print("   - 用途: 分享给朋友，在浏览器中观看")
    print("   - 特点: 永久有效，用户友好")
    print()
    
    print("2. 🎬 视频下载链接")
    print("   - 格式: https://www.douyin.com/aweme/v1/play/...")
    print("   - 用途: 直接下载视频文件")
    print("   - 特点: 包含签名，有时效性")
    print()
    
    print("3. 🖼️ 封面图片链接")
    print("   - 格式: https://p3-pc-sign.douyinpic.com/...")
    print("   - 用途: 获取视频封面图片")
    print("   - 特点: 可直接在浏览器中查看")
    print()
    
    print("4. 🎵 背景音乐链接")
    print("   - 格式: https://lf26-music-east.douyinstatic.com/...")
    print("   - 用途: 下载视频背景音乐")
    print("   - 特点: MP3格式，可直接下载")
    print()
    
    print("5. 🎯 全部链接类型")
    print("   - 包含以上所有类型的链接")
    print("   - 用途: 获取完整的链接信息")
    print("   - 特点: 信息最全面")
    print()
    
    print("💡 使用方法:")
    print("1. 运行: python quick_start.py")
    print("2. 输入搜索关键词")
    print("3. 设置爬取数量")
    print("4. 选择链接类型 (1-5)")
    print("5. 扫码登录并等待爬取完成")
    print("6. 程序会自动生成对应类型的链接文件")
    print()
    
    # 如果有现有数据，展示示例
    if os.path.exists("data/douyin/json/search_contents_2025-09-11.json"):
        show_examples()


def show_examples():
    """显示链接示例"""
    print("📋 基于你的爬取数据的链接示例:")
    print("-" * 50)
    
    try:
        with open("data/douyin/json/search_contents_2025-09-11.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if data:
            video = data[0]  # 取第一个视频作为示例
            title = video.get('title', '无标题')[:40] + "..."
            
            print(f"🎬 示例视频: {title}")
            print(f"👤 作者: {video.get('nickname', '未知')}")
            print()
            print("🔗 不同类型的链接:")
            
            # 标准链接
            if video.get('aweme_url'):
                print(f"📱 标准链接: {video.get('aweme_url')}")
            
            # 下载链接
            if video.get('video_download_url'):
                download_url = video.get('video_download_url')
                print(f"🎬 下载链接: {download_url[:60]}...")
            
            # 封面链接
            if video.get('cover_url'):
                cover_url = video.get('cover_url')
                print(f"🖼️ 封面链接: {cover_url[:60]}...")
            
            # 音乐链接
            if video.get('music_download_url'):
                music_url = video.get('music_download_url')
                print(f"🎵 音乐链接: {music_url[:60]}...")
            
            print()
            print("💡 选择类型1会提取标准链接，类型2会提取下载链接，以此类推")
            
    except Exception as e:
        print(f"读取示例数据失败: {e}")


def create_sample_output():
    """创建示例输出文件"""
    print("\n📄 创建示例输出文件...")
    
    if not os.path.exists("data/douyin/json/search_contents_2025-09-11.json"):
        print("❌ 没有找到爬取数据，无法创建示例")
        return
    
    try:
        with open("data/douyin/json/search_contents_2025-09-11.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建标准链接示例文件
        filename = f"sample_standard_links_{timestamp}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("📱 抖音标准视频链接 (示例)\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            
            for i, video in enumerate(data[:3], 1):  # 只取前3个作为示例
                title = video.get('title', '无标题')
                author = video.get('nickname', '未知')
                url = video.get('aweme_url', '')
                likes = video.get('liked_count', 0)
                
                f.write(f"{i:2d}. {title}\n")
                f.write(f"    👤 作者: {author}\n")
                f.write(f"    👍 点赞: {likes}\n")
                f.write(f"    🔗 链接: {url}\n")
                f.write("-" * 60 + "\n")
        
        print(f"✅ 示例文件已创建: {filename}")
        print("💡 这就是选择类型1时会生成的文件格式")
        
    except Exception as e:
        print(f"创建示例文件失败: {e}")


def main():
    """主函数"""
    demo_link_selection()
    
    print("\n🎯 要创建示例输出文件吗？")
    choice = input("输入 y 创建示例，其他键跳过: ").strip().lower()
    
    if choice == 'y':
        create_sample_output()
    
    print("\n🚀 现在你可以运行以下命令来体验新功能:")
    print("   python quick_start.py")
    print()
    print("✨ 新功能特点:")
    print("• 🎯 可选择提取特定类型的链接")
    print("• 📁 自动生成格式化的文件")
    print("• 🔗 支持5种不同的链接类型")
    print("• 💾 结果保存为易读的文本格式")
    print()
    print("🎉 享受你的抖音爬虫新功能！")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 演示被取消")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
        import traceback
        traceback.print_exc()
