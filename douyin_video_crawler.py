#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音视频链接爬取脚本
通过关键词搜索抖音视频并提取视频链接

使用方法:
1. 修改 KEYWORDS 变量设置要搜索的关键词
2. 运行脚本: python douyin_video_crawler.py
3. 扫码登录抖音
4. 等待爬取完成，查看结果

作者: AI Assistant
日期: 2025-09-11
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import CrawlerFactory
from tools import utils


class DouyinVideoLinkExtractor:
    """抖音视频链接提取器"""
    
    def __init__(self, keywords: str = None, max_videos: int = 20):
        """
        初始化提取器
        
        Args:
            keywords: 搜索关键词，多个关键词用逗号分隔
            max_videos: 最大爬取视频数量
        """
        self.keywords = keywords or "美食,旅游,科技"
        self.max_videos = max_videos
        self.results = []
        
    async def extract_video_links(self):
        """提取视频链接"""
        print(f"🚀 开始爬取抖音视频链接...")
        print(f"📝 搜索关键词: {self.keywords}")
        print(f"📊 最大爬取数量: {self.max_videos}")
        print("-" * 50)
        
        # 设置配置
        config.PLATFORM = "dy"
        config.KEYWORDS = self.keywords
        config.CRAWLER_TYPE = "search"
        config.CRAWLER_MAX_NOTES_COUNT = self.max_videos
        config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
        config.ENABLE_GET_COMMENTS = False  # 不爬取评论
        config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
        
        try:
            # 创建爬虫实例
            crawler = CrawlerFactory.create_crawler(platform="dy")
            
            # 开始爬取
            await crawler.start()
            
            # 读取爬取结果
            await self._load_results()
            
            # 显示结果
            self._display_results()
            
            # 保存链接到文件
            self._save_links_to_file()
            
        except Exception as e:
            print(f"❌ 爬取过程中出现错误: {e}")
            utils.logger.error(f"DouyinVideoLinkExtractor error: {e}")
    
    async def _load_results(self):
        """加载爬取结果"""
        data_dir = "data/douyin"
        if not os.path.exists(data_dir):
            print("⚠️  未找到数据目录，可能爬取失败")
            return
        
        # 查找最新的JSON文件
        json_files = [f for f in os.listdir(data_dir) if f.endswith('.json') and 'contents' in f]
        if not json_files:
            print("⚠️  未找到爬取结果文件")
            return
        
        # 按修改时间排序，获取最新文件
        latest_file = max(json_files, key=lambda x: os.path.getmtime(os.path.join(data_dir, x)))
        file_path = os.path.join(data_dir, latest_file)
        
        print(f"📂 读取结果文件: {latest_file}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print("⚠️  结果文件为空")
                    return
                
                # 处理JSONL格式（每行一个JSON对象）
                lines = content.split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            self.results.append(data)
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            print(f"❌ 读取结果文件失败: {e}")
    
    def _display_results(self):
        """显示爬取结果"""
        if not self.results:
            print("😔 未获取到任何视频数据")
            return
        
        print(f"\n🎉 成功获取到 {len(self.results)} 个视频!")
        print("=" * 80)
        
        for i, video in enumerate(self.results, 1):
            print(f"\n📹 视频 {i}:")
            print(f"   标题: {video.get('title', '无标题')[:50]}...")
            print(f"   作者: {video.get('nickname', '未知')}")
            print(f"   点赞: {video.get('liked_count', 0)}")
            print(f"   评论: {video.get('comment_count', 0)}")
            print(f"   分享: {video.get('share_count', 0)}")
            print(f"   视频链接: {video.get('aweme_url', '无链接')}")
            print(f"   下载链接: {video.get('video_download_url', '无下载链接')}")
            print(f"   封面链接: {video.get('cover_url', '无封面')}")
            print("-" * 80)
    
    def _save_links_to_file(self):
        """保存视频链接到文件"""
        if not self.results:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"douyin_video_links_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"抖音视频链接提取结果\n")
                f.write(f"搜索关键词: {self.keywords}\n")
                f.write(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"视频数量: {len(self.results)}\n")
                f.write("=" * 80 + "\n\n")
                
                for i, video in enumerate(self.results, 1):
                    f.write(f"视频 {i}:\n")
                    f.write(f"标题: {video.get('title', '无标题')}\n")
                    f.write(f"作者: {video.get('nickname', '未知')}\n")
                    f.write(f"视频页面: {video.get('aweme_url', '无链接')}\n")
                    f.write(f"视频下载: {video.get('video_download_url', '无下载链接')}\n")
                    f.write(f"封面图片: {video.get('cover_url', '无封面')}\n")
                    f.write(f"点赞数: {video.get('liked_count', 0)}\n")
                    f.write(f"评论数: {video.get('comment_count', 0)}\n")
                    f.write(f"分享数: {video.get('share_count', 0)}\n")
                    f.write("-" * 50 + "\n")
                
            print(f"\n💾 视频链接已保存到文件: {filename}")
            
        except Exception as e:
            print(f"❌ 保存链接文件失败: {e}")


async def main():
    """主函数"""
    print("🎬 抖音视频链接爬取工具")
    print("=" * 50)
    
    # 可以在这里修改搜索关键词和爬取数量
    keywords = input("请输入搜索关键词（多个关键词用逗号分隔，直接回车使用默认关键词）: ").strip()
    if not keywords:
        keywords = "美食,旅游,科技"
    
    try:
        max_videos = int(input("请输入最大爬取数量（直接回车使用默认20个）: ").strip() or "20")
    except ValueError:
        max_videos = 20
    
    print(f"\n开始爬取，关键词: {keywords}，数量: {max_videos}")
    print("请准备好手机扫码登录抖音...")
    
    extractor = DouyinVideoLinkExtractor(keywords=keywords, max_videos=max_videos)
    await extractor.extract_video_links()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
