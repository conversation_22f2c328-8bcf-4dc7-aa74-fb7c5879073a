#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音号专门爬取工具

专门爬取指定抖音号的所有视频
"""

import asyncio
import json
import os
import sys
import re
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_creator_config(creator_id, max_count=50):
    """设置抖音号爬取配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.CRAWLER_TYPE = "creator"  # 创作者模式
    config.CRAWLER_MAX_NOTES_COUNT = max_count  # 最大爬取数量
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = False  # 不爬取评论
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    config.CRAWLER_MAX_SLEEP_SEC = 3  # 爬取间隔
    
    # 设置要爬取的创作者ID列表
    config.DY_CREATOR_ID_LIST = [creator_id]


def parse_creator_input(creator_input):
    """解析抖音号输入"""
    creator_input = creator_input.strip()
    
    # 如果是用户主页链接
    if 'douyin.com/user/' in creator_input:
        # 提取sec_user_id
        match = re.search(r'/user/([^/?]+)', creator_input)
        if match:
            return match.group(1)
    
    # 如果是sec_user_id格式
    if creator_input.startswith('MS4wLjABAAAA'):
        return creator_input
    
    # 其他情况直接返回（可能是抖音号）
    return creator_input


def load_crawled_data():
    """加载爬取的数据"""
    data_dir = "data/douyin"
    results = []
    
    if not os.path.exists(data_dir):
        return results
    
    # 查找最新的JSON文件
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")
    
    return results


def save_creator_videos(data, creator_id):
    """保存创作者视频信息"""
    if not data:
        print("❌ 没有获取到视频数据")
        return
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 获取创作者信息
    creator_name = "未知作者"
    if data:
        creator_name = data[0].get('nickname', '未知作者')
    
    # 保存简洁链接列表
    links_file = f"{creator_name}_视频链接_{timestamp}.txt"
    with open(links_file, 'w', encoding='utf-8') as f:
        f.write(f"{creator_name} 的抖音视频链接\n")
        f.write(f"抖音号ID: {creator_id}\n")
        f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 50 + "\n\n")
        
        for i, video in enumerate(data, 1):
            url = video.get('aweme_url', '')
            if url:
                f.write(f"{url}\n")
    
    print(f"✅ 简洁链接已保存到: {links_file}")
    
    # 保存详细信息
    detail_file = f"{creator_name}_视频详情_{timestamp}.txt"
    with open(detail_file, 'w', encoding='utf-8') as f:
        f.write(f"{creator_name} 的抖音视频详情\n")
        f.write(f"抖音号ID: {creator_id}\n")
        f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"视频数量: {len(data)}\n")
        f.write("=" * 60 + "\n\n")
        
        for i, video in enumerate(data, 1):
            title = video.get('title', '无标题')
            url = video.get('aweme_url', '')
            likes = video.get('liked_count', 0)
            comments = video.get('comment_count', 0)
            shares = video.get('share_count', 0)
            
            f.write(f"{i:2d}. {title}\n")
            f.write(f"    🔗 链接: {url}\n")
            f.write(f"    👍 点赞: {likes} | 💬 评论: {comments} | 🔄 分享: {shares}\n")
            f.write("-" * 60 + "\n")
    
    print(f"📄 详细信息已保存到: {detail_file}")
    
    # 显示统计信息
    total_likes = sum(int(str(v.get('liked_count', 0)).replace(',', '')) for v in data)
    total_comments = sum(int(str(v.get('comment_count', 0)).replace(',', '')) for v in data)
    
    print(f"\n📊 {creator_name} 的视频统计:")
    print(f"   📹 视频数量: {len(data)}")
    print(f"   👍 总点赞数: {total_likes:,}")
    print(f"   💬 总评论数: {total_comments:,}")
    print(f"   📱 平均点赞: {total_likes//len(data) if data else 0:,}")


async def main():
    """主函数"""
    print("👤 抖音号专门爬取工具")
    print("=" * 50)
    print("📋 功能说明:")
    print("✅ 专门爬取指定抖音号的所有视频")
    print("✅ 获取该作者的完整作品列表")
    print("✅ 生成简洁的视频链接文件")
    print("✅ 提供详细的统计信息")
    print()
    
    # 获取抖音号信息
    print("💡 支持的输入格式:")
    print("   1. 用户主页链接: https://www.douyin.com/user/xxx")
    print("   2. sec_user_id: MS4wLjABAAAA...")
    print("   3. 抖音号: douyin123456")
    print()
    
    creator_input = input("请输入抖音号、主页链接或sec_user_id: ").strip()
    if not creator_input:
        print("❌ 必须输入抖音号信息")
        return
    
    creator_id = parse_creator_input(creator_input)
    
    try:
        max_count = input("请输入最大爬取数量（回车使用默认50）: ").strip()
        max_count = int(max_count) if max_count else 50
    except ValueError:
        max_count = 50
        print(f"使用默认数量: {max_count}")
    
    print(f"\n✅ 配置完成:")
    print(f"   抖音号: {creator_id}")
    print(f"   数量: {max_count}")
    print("⚠️  注意: 爬取创作者视频可能需要较长时间")
    print()
    
    input("按回车键开始爬取...")
    
    # 设置配置
    setup_creator_config(creator_id, max_count)
    
    print("\n🚀 开始爬取抖音号视频...")
    print("请准备手机扫码登录抖音!")
    print("-" * 50)
    
    try:
        # 运行爬虫
        await crawler_main()
        
        print("\n🎉 爬取完成!")
        
        # 加载并保存数据
        data = load_crawled_data()
        
        if data:
            save_creator_videos(data, creator_id)
            print("\n✅ 抖音号视频爬取完成!")
        else:
            print("❌ 没有获取到视频数据")
            print("可能的原因:")
            print("- 抖音号不存在或输入错误")
            print("- 该用户设置了隐私权限")
            print("- 网络连接问题")
            print("- 需要使用正确的sec_user_id格式")
        
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("💡 请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
