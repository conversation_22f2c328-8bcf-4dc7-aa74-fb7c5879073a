#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬取指定的抖音视频

专门爬取: https://v.douyin.com/feDuNe0NS-0/
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from main import main as crawler_main


def setup_config():
    """设置配置"""
    config.PLATFORM = "dy"  # 抖音平台
    config.CRAWLER_TYPE = "detail"  # 详情模式
    config.CRAWLER_MAX_NOTES_COUNT = 1  # 只爬取一个视频
    config.ENABLE_GET_MEIDAS = False  # 不下载媒体文件
    config.ENABLE_GET_COMMENTS = True  # 获取评论信息
    config.SAVE_DATA_OPTION = "json"  # 保存为JSON格式
    config.LOGIN_TYPE = "qrcode"  # 二维码登录
    config.HEADLESS = False  # 显示浏览器窗口
    
    # 设置要爬取的视频链接
    config.DY_SPECIFIED_ID_LIST = ["https://v.douyin.com/feDuNe0NS-0/"]


def load_video_data():
    """加载爬取的视频数据"""
    data_dir = "data/douyin"
    results = []
    
    if not os.path.exists(data_dir):
        return results
    
    # 查找最新的JSON文件
    for root, _, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content.startswith('['):
                            # JSON数组格式
                            data = json.loads(content)
                            results.extend(data)
                        else:
                            # JSONL格式
                            lines = content.split('\n')
                            for line in lines:
                                if line.strip():
                                    try:
                                        data = json.loads(line)
                                        results.append(data)
                                    except json.JSONDecodeError:
                                        continue
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {e}")
    
    return results


def save_video_info(video_data):
    """保存视频信息"""
    if not video_data:
        print("没有数据可保存")
        return
    
    video = video_data[0] if isinstance(video_data, list) else video_data
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存简洁链接
    link_file = f"程序员王老师视频链接_{timestamp}.txt"
    with open(link_file, 'w', encoding='utf-8') as f:
        f.write("程序员王老师的抖音视频\n")
        f.write(f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 40 + "\n\n")
        
        title = video.get('title', '无标题')
        author = video.get('nickname', '未知作者')
        aweme_url = video.get('aweme_url', '')
        
        f.write(f"标题: {title}\n")
        f.write(f"作者: {author}\n")
        f.write(f"链接: {aweme_url}\n")
    
    print(f"✅ 视频链接已保存到: {link_file}")
    
    # 保存详细信息
    detail_file = f"程序员王老师视频详情_{timestamp}.json"
    with open(detail_file, 'w', encoding='utf-8') as f:
        json.dump(video, f, ensure_ascii=False, indent=2)
    
    print(f"📄 详细信息已保存到: {detail_file}")
    
    # 显示基本信息
    print(f"\n🎬 视频信息:")
    print(f"📝 标题: {video.get('title', '无标题')}")
    print(f"👤 作者: {video.get('nickname', '未知作者')}")
    print(f"🔗 链接: {video.get('aweme_url', '')}")
    print(f"👍 点赞: {video.get('liked_count', 0)}")
    print(f"💬 评论: {video.get('comment_count', 0)}")
    print(f"🔄 分享: {video.get('share_count', 0)}")


async def main():
    """主函数"""
    print("🎬 爬取程序员王老师的抖音视频")
    print("视频链接: https://v.douyin.com/feDuNe0NS-0/")
    print("=" * 50)
    
    input("按回车键开始爬取...")
    
    # 设置配置
    setup_config()
    
    print("\n🚀 开始爬取视频信息...")
    print("请准备手机扫码登录抖音!")
    print("-" * 50)
    
    try:
        # 运行爬虫
        await crawler_main()
        
        print("\n🎉 爬取完成!")
        
        # 加载并保存视频数据
        video_data = load_video_data()
        
        if video_data:
            save_video_info(video_data)
            print("\n✅ 视频信息获取完成!")
        else:
            print("❌ 没有获取到视频数据")
            print("可能的原因:")
            print("- 视频链接已失效")
            print("- 视频设置了隐私权限")
            print("- 网络连接问题")
        
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        print("请检查网络连接和登录状态")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
