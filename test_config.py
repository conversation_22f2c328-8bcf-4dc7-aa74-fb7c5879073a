#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本

用于验证抖音爬虫的配置是否正确
"""

import os
import sys
import importlib.util

def test_imports():
    """测试必要的模块导入"""
    print("🔍 测试模块导入...")
    
    required_modules = [
        'asyncio',
        'json',
        'httpx',
        'playwright',
        'aiofiles'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ 缺少依赖模块: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有必要模块导入成功")
    return True


def test_project_structure():
    """测试项目结构"""
    print("\n🔍 测试项目结构...")
    
    required_files = [
        'main.py',
        'config/base_config.py',
        'config/dy_config.py',
        'media_platform/douyin/core.py',
        'media_platform/douyin/client.py',
        'base/base_crawler.py'
    ]
    
    required_dirs = [
        'config',
        'media_platform',
        'media_platform/douyin',
        'base',
        'store',
        'tools'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查目录
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
            print(f"  ❌ 目录: {dir_path}")
        else:
            print(f"  ✅ 目录: {dir_path}")
    
    # 检查文件
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            print(f"  ❌ 文件: {file_path}")
        else:
            print(f"  ✅ 文件: {file_path}")
    
    if missing_files or missing_dirs:
        print(f"\n❌ 项目结构不完整")
        if missing_dirs:
            print(f"缺少目录: {', '.join(missing_dirs)}")
        if missing_files:
            print(f"缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 项目结构完整")
    return True


def test_config():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    
    try:
        # 添加项目根目录到Python路径
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        import config
        
        # 检查关键配置
        configs_to_check = [
            ('PLATFORM', 'dy'),
            ('KEYWORDS', str),
            ('CRAWLER_TYPE', 'search'),
            ('SAVE_DATA_OPTION', str),
            ('CRAWLER_MAX_NOTES_COUNT', int),
            ('ENABLE_GET_MEIDAS', bool),
            ('ENABLE_GET_COMMENTS', bool)
        ]
        
        for config_name, expected_type in configs_to_check:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                if expected_type == str:
                    print(f"  ✅ {config_name}: {value}")
                elif isinstance(expected_type, type):
                    if isinstance(value, expected_type):
                        print(f"  ✅ {config_name}: {value} ({type(value).__name__})")
                    else:
                        print(f"  ⚠️  {config_name}: {value} (类型不匹配)")
                else:
                    if value == expected_type:
                        print(f"  ✅ {config_name}: {value}")
                    else:
                        print(f"  ⚠️  {config_name}: {value} (期望: {expected_type})")
            else:
                print(f"  ❌ 缺少配置: {config_name}")
        
        print("✅ 配置文件加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False


def test_scripts():
    """测试脚本文件"""
    print("\n🔍 测试脚本文件...")
    
    scripts = [
        'quick_start.py',
        'douyin_video_crawler.py',
        'view_results.py'
    ]
    
    for script in scripts:
        if os.path.exists(script):
            print(f"  ✅ {script}")
            
            # 简单的语法检查
            try:
                with open(script, 'r', encoding='utf-8') as f:
                    content = f.read()
                compile(content, script, 'exec')
                print(f"    ✅ 语法检查通过")
            except SyntaxError as e:
                print(f"    ❌ 语法错误: {e}")
                return False
        else:
            print(f"  ❌ {script} 不存在")
            return False
    
    print("✅ 所有脚本文件正常")
    return True


def test_data_directory():
    """测试数据目录"""
    print("\n🔍 测试数据目录...")
    
    data_dir = "data"
    douyin_dir = "data/douyin"
    
    # 创建数据目录（如果不存在）
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"  ✅ 创建目录: {data_dir}")
    else:
        print(f"  ✅ 目录存在: {data_dir}")
    
    if not os.path.exists(douyin_dir):
        os.makedirs(douyin_dir)
        print(f"  ✅ 创建目录: {douyin_dir}")
    else:
        print(f"  ✅ 目录存在: {douyin_dir}")
    
    # 检查写入权限
    try:
        test_file = os.path.join(douyin_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"  ✅ 目录可写: {douyin_dir}")
    except Exception as e:
        print(f"  ❌ 目录不可写: {e}")
        return False
    
    print("✅ 数据目录配置正常")
    return True


def main():
    """主测试函数"""
    print("🧪 抖音爬虫配置测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("项目结构", test_project_structure),
        ("配置文件", test_config),
        ("脚本文件", test_scripts),
        ("数据目录", test_data_directory)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！配置正确，可以开始使用爬虫了。")
        print("\n🚀 快速开始:")
        print("   python quick_start.py")
        return True
    else:
        print("❌ 部分测试失败，请检查配置后重试。")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户取消")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
