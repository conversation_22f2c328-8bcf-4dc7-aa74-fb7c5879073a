# 👤 抖音号专门爬取功能使用说明

## 🎯 新功能概述

现在 `quick_start.py` 支持两种爬取模式：
1. **🔍 关键词搜索模式** - 原有功能，通过关键词搜索视频
2. **👤 抖音号专门爬取模式** - 新功能，专门爬取指定抖音号的所有视频

## 🚀 使用方法

### 方法1: 使用升级版快速启动（推荐）

```bash
python quick_start.py
```

启动后会提示选择模式：
- 选择 `1` - 关键词搜索模式
- 选择 `2` - 抖音号专门爬取模式

### 方法2: 使用专门的抖音号爬取工具

```bash
python douyin_creator_crawler.py
```

直接进入抖音号爬取模式。

## 👤 抖音号爬取模式详解

### 📋 支持的输入格式

1. **用户主页链接**（推荐）
   ```
   https://www.douyin.com/user/MS4wLjABAAAA...
   ```

2. **sec_user_id**（最准确）
   ```
   MS4wLjABAAAAVmG_pTXp3pvTEwF7Cm3te2-s_RDjXsCMf3n4sgs-63u-0xRsmvBdm6gj3rjNKaR-
   ```

3. **抖音号**（可能需要转换）
   ```
   douyin123456
   ```

### 🔍 如何获取抖音号信息

#### 方法1: 通过抖音APP
1. 打开抖音APP
2. 进入目标用户主页
3. 点击右上角分享按钮
4. 选择"复制链接"
5. 粘贴到程序中

#### 方法2: 通过浏览器
1. 在浏览器中打开抖音网页版
2. 搜索并进入目标用户主页
3. 复制浏览器地址栏中的链接
4. 粘贴到程序中

### 📊 爬取结果

爬取完成后会生成：

#### 📄 简洁链接文件
```
[作者名]_视频链接_时间戳.txt
```
内容格式：
```
程序员王老师 的抖音视频链接
抖音号ID: MS4wLjABAAAA...
获取时间: 2025-09-13 15:30:00
视频数量: 25

https://www.douyin.com/video/7547357379264859431
https://www.douyin.com/video/7543227968785976628
...
```

#### 📋 详细信息文件
```
[作者名]_视频详情_时间戳.txt
```
包含每个视频的：
- 标题
- 链接
- 点赞数、评论数、分享数

#### 📊 统计信息
- 视频总数量
- 总点赞数
- 总评论数
- 平均点赞数

## ⚙️ 配置说明

### 抖音号爬取配置
```python
CRAWLER_TYPE = "creator"           # 创作者模式
CRAWLER_MAX_NOTES_COUNT = 50       # 默认爬取50个视频
DY_CREATOR_ID_LIST = [creator_id]  # 创作者ID列表
```

### 推荐设置
- **爬取数量**: 50个（可以获取大部分近期视频）
- **评论爬取**: 关闭（加快爬取速度）
- **媒体下载**: 关闭（只获取链接）

## 💡 使用建议

### 1. 选择合适的抖音号格式
- **最推荐**: 用户主页链接（最准确）
- **次推荐**: sec_user_id（直接有效）
- **需注意**: 抖音号（可能需要转换）

### 2. 设置合理的爬取数量
- **个人用户**: 20-50个视频
- **活跃创作者**: 50-100个视频
- **大V用户**: 100+个视频

### 3. 注意爬取时间
- 创作者模式比搜索模式耗时更长
- 视频数量越多，耗时越长
- 建议在网络稳定时进行

## 🔧 故障排除

### 问题1: 找不到用户或没有数据
**可能原因:**
- 抖音号输入错误
- 用户设置了隐私权限
- 用户没有公开视频

**解决方案:**
- 检查抖音号格式是否正确
- 尝试使用用户主页链接
- 确认该用户有公开视频

### 问题2: 爬取数量少于预期
**可能原因:**
- 用户实际视频数量较少
- 部分视频设置了隐私
- 网络问题导致中断

**解决方案:**
- 检查用户实际视频数量
- 增加爬取数量设置
- 重新尝试爬取

### 问题3: sec_user_id 格式错误
**可能原因:**
- 复制链接不完整
- 包含了多余的参数

**解决方案:**
- 重新复制完整的用户主页链接
- 确保链接格式正确
- 使用程序自动解析功能

## 📋 完整使用流程

1. **启动程序**
   ```bash
   python quick_start.py
   ```

2. **选择模式**
   - 输入 `2` 选择抖音号专门爬取模式

3. **输入抖音号信息**
   - 粘贴用户主页链接或输入sec_user_id

4. **设置爬取数量**
   - 输入想要爬取的视频数量（建议50）

5. **选择输出格式**
   - 选择 `1` 获得简洁链接列表

6. **登录并等待**
   - 扫码登录抖音
   - 等待爬取完成

7. **查看结果**
   - 检查生成的链接文件
   - 查看统计信息

## 🎉 优势特点

- ✅ **专门针对**: 只爬取指定用户的视频
- ✅ **完整覆盖**: 获取该用户的所有公开视频
- ✅ **详细统计**: 提供完整的数据分析
- ✅ **格式统一**: 和关键词搜索结果格式一致
- ✅ **易于管理**: 按作者分类保存文件

现在你可以专门爬取任何抖音号的所有视频了！🎬👤
